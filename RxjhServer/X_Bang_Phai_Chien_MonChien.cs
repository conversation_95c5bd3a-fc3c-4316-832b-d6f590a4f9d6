using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Timers;
using HeroYulgang.Helpers;
using RxjhServer.Database;

namespace RxjhServer;

public class X_<PERSON>_<PERSON>ai_Chien_MonChien
{
	[Serializable]
	[CompilerGenerated]
	private sealed class Class0
	{
		public static readonly Class0 class0_0 = new();

		internal int method_0(KeyValuePair<string, int> keyValuePair_0)
		{
			return keyValuePair_0.Value;
		}
	}

	private object AsyncLock = new();

	private Dictionary<string, int> BangDiem = new();

	private System.Timers.Timer timer_0;

	private System.Timers.Timer timer_1;

	private System.Timers.Timer timer_2;

	private System.Timers.Timer timer_3;

	private DateTime dateTime_0;

	private DateTime dateTime_1;

	private DateTime dateTime_2;

	public X_Bang_Phai_Chien_MonChien()
	{
		try
		{
			World.MonChien_ProgressNew = 1;
			World.HelpList = new();
			dateTime_0 = DateTime.Now.AddMinutes(10.0);
			timer_0 = new(60000.0);
			timer_0.Elapsed += 门主申请记时器KetThucSuKien;
			timer_0.Enabled = true;
			timer_0.AutoReset = true;
			foreach (var value in World.allConnectedChars.Values)
			{
				value.RollingAnnouncement(4500);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Máu BangChien PrepareTheChronographTimeEndEvent error：" + ex);
		}
	}

	public void 门主申请记时器KetThucSuKien(object sender, ElapsedEventArgs e)
	{
		try
		{
			var num = (int)dateTime_0.Subtract(DateTime.Now).TotalSeconds;
			foreach (var value in World.allConnectedChars.Values)
			{
				value.HeThongNhacNho("Bang chiến còn " + (num + 59) / 60 + " khắc để kết thúc. Bang chủ hãy triệu tập quần hùng đến Thượng Quan Thành chuẩn bị xuất trận!", 6, "Thiên Cơ Lệnh");
			}
			if (num > 0)
			{
				return;
			}
			timer_0.Enabled = false;
			timer_0.Close();
			timer_0.Dispose();
			foreach (var value2 in World.HelpList.Values)
			{
				using (new Lock(value2.DanhSachUngVien, "BangChienDanhSachUngVien"))
				{
					foreach (var value3 in value2.DanhSachUngVien.Values)
					{
						value3.SystemNotification("Môn chiến phối đôi thành công, mời chuẩn bị sẵn sàng chiến đấu, 10 Giây sau bắt đầu bang chiến!");
					}
				}
			}
			dateTime_1 = DateTime.Now.AddSeconds(10.0);
			timer_1 = new(5000.0);
			timer_1.Elapsed += PrepareTheChronographTimeEndEvent;
			timer_1.Enabled = true;
			timer_1.AutoReset = true;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "BangChien Đăng ký đồng hồ bấm giờ KetThucSuKien error：" + ex);
		}
	}

	public void PrepareTheChronographTimeEndEvent(object sender, ElapsedEventArgs e)
	{
		try
		{
			if ((int)dateTime_1.Subtract(DateTime.Now).TotalSeconds > 0)
			{
				return;
			}
			foreach (var value in World.HelpList.Values)
			{
				foreach (var value2 in value.DanhSachUngVien.Values)
				{
					if (value2.Player_Level >= 100 && value2.NhanVatToaDo_BanDo == 1201 && value2.NhanVat_HP > 0 && !value2.PlayerTuVong && !value2.Exiting)
					{
						continue;
					}
					LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Debug, "BangPhai [" + value.DangKyTenBangPhai + "] hội viên [" + value2.CharacterName + "] - Không đủ điều kiện tham gia các trận đánh cửa - |DangCap:" + value2.Player_Level + "|BanDo:" + value2.NhanVatToaDo_BanDo + "|SinhMenh:" + value2.NhanVat_HP + "|ThoatGame:" + value2.Exiting + "|进店:" + value2.InTheShop + "|TuVong:" + value2.PlayerTuVong);
					value.DanhSachUngVien.Remove(value2.SessionID);
					if (value.DanhSachUngVien.Count >= 5)
					{
						continue;
					}
					LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Debug, "BangPhai[" + value.DangKyTenBangPhai + "] - Người tham chiến dưới 5 người bị truất quyền tham chiến.");
					using (new Lock(World.HelpList, "HelpList"))
					{
						World.HelpList.Remove(value.DangKy_BangPhaiID);
						if (World.HelpNameList != null && World.HelpNameList.Count > 0 && World.HelpNameList.ContainsKey(value.DangKy_BangPhaiID))
						{
							World.HelpNameList.Remove(value.DangKy_BangPhaiID);
						}
					}
				}
			}
			if (World.HelpList.Count < 2)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Debug, "Số lượng BangChienBangPhai tham gia ít hơn BangPhai 2, BangChien đã hủy.");
				foreach (var value3 in World.HelpList.Values)
				{
					foreach (var value4 in value3.DanhSachUngVien.Values)
					{
						value4.SafeMode = 0;
						if (value4.GangCharacterLevel == 6)
						{
							value4.HeThongNhacNho("Không thể tiến hành bang chiến do không có bang phái phù hợp, Thiên cơ các hoàn lại [" + World.HeThong_MonChien_CanNguyenBao + "] Nguyên Bảo!", 6, "Thiên Cơ Lệnh");
							value4.KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop();
							value4.KiemSoatNguyenBao_SoLuong(World.HeThong_MonChien_CanNguyenBao, 1);
							value4.Save_NguyenBaoData();
							LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Debug, "Trở lại với thủ lĩnh băng đảng Bang Phái [" + value4.CharacterName + "] [" + World.HeThong_MonChien_CanNguyenBao + "] Point");
						}
						else
						{
							value4.HeThongNhacNho("Không thể tiến hành các trận công môn do không có bang phái phù hợp!", 6, "Thiên Cơ Lệnh");
						}
					}
				}
				World.BangChienThang_ID = 0;
				if (World.HelpNameList != null && World.HelpNameList.Count > 0)
				{
					World.HelpNameList.Clear();
				}
				Dispose();
				return;
			}
			World.MonChien_ProgressNew = 2;
			World.CoMoRa_HeThong_MonChien = 0;
			timer_1.Enabled = false;
			timer_1.Close();
			timer_1.Dispose();
			dateTime_2 = DateTime.Now.AddMinutes(30.0);
			timer_2 = new(60000.0);
			timer_2.Elapsed += StartToFightTheChronographTimeEndEvent;
			timer_2.Enabled = true;
			timer_2.AutoReset = true;
			var num = 0;
			var num2 = 0;
			var num3 = 0;
			foreach (var value5 in World.HelpList.Values)
			{
				switch (num)
				{
				case 0:
					num2 = 0;
					num3 = 300;
					break;
				case 1:
					num2 = -300;
					num3 = 0;
					break;
				case 2:
					num2 = 300;
					num3 = 0;
					break;
				case 3:
					num2 = 0;
					num3 = -300;
					break;
				}
				using (new Lock(value5.DanhSachUngVien, "BangChienDanhSachUngVien"))
				{
					foreach (var value6 in value5.DanhSachUngVien.Values)
					{
						value6.Mobile(num2, num3, 15f, 7301, 0);
						value6.HelpStartPrompt(1, 0);
						value6.SystemNotification("Bang chiến Bắt đầu, thỏa thích chém giết đi. Cuối cùng bang chiến thắng Sẽ có cơ hội tăng lên Level!");
						value6.SwitchPkMode(2);
						value6.SafeMode = 0;
					}
				}
				num++;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "BangChien PrepareTheChronographTimeEndEvent error：" + ex);
		}
	}

	public void StartToFightTheChronographTimeEndEvent(object sender, ElapsedEventArgs e)
	{
		try
		{
			BangDiem.Clear();
			var num = (int)dateTime_2.Subtract(DateTime.Now).TotalSeconds;
			foreach (var value in World.HelpList.Values)
			{
				BangDiem.Add(value.DangKyTenBangPhai, value.DiemSoHienTai);
			}
			var num2 = 0;
			var text = string.Empty;
			foreach (var item in BangDiem.OrderByDescending((KeyValuePair<string, int> keyValuePair_0) => keyValuePair_0.Value))
			{
				if (num2 >= 3)
				{
					break;
				}
				text = text + item.Key + ":" + item.Value + " ";
				num2++;
			}
			foreach (var value2 in World.HelpList.Values)
			{
				foreach (var value3 in value2.DanhSachUngVien.Values)
				{
					if (value3.NhanVatToaDo_BanDo == 7301)
					{
						value3.HeThongNhacNho(text, 7, "Thiên Cơ Lệnh");
					}
				}
			}
			if (num <= 60)
			{
				foreach (var value4 in World.HelpList.Values)
				{
					foreach (var value5 in value4.DanhSachUngVien.Values)
					{
						if (value5.NhanVatToaDo_BanDo == 7301)
						{
							value5.HeThongNhacNho("Thời khắc giao phong lần thứ [" + num + "] sắp bắt đầu, các hiệp sĩ hãy chuẩn bị xuất chiêu!", 6, "Thiên Cơ Lệnh");
						}
					}
				}
			}
			if (num > 0)
			{
				return;
			}
			World.MonChien_ProgressNew = 3;
			World.CoMoRa_HeThong_MonChien = 0;
			foreach (var value6 in World.HelpList.Values)
			{
				foreach (var value7 in value6.DanhSachUngVien.Values)
				{
					if (value7.NhanVatToaDo_BanDo == 7301)
					{
						value7.SafeMode = 1;
						value7.HeThongNhacNho("Thời gian giao đấu đã kết thúc, sau 10 tức sẽ công bố kết quả luận anh hùng!", 6, "Thiên Cơ Lệnh");
					}
				}
			}
			timer_2.Enabled = false;
			timer_2.Close();
			timer_2.Dispose();
			timer_3 = new(10000.0);
			timer_3.Elapsed += ThoiGianKetThucSuKien3;
			timer_3.Enabled = true;
			timer_3.AutoReset = false;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "BangChien StartToFightTheChronographTimeEndEvent error：" + ex);
		}
	}

	public void ThoiGianKetThucSuKien3(object sender, ElapsedEventArgs e)
	{
		try
		{
			var num = 0;
			var num2 = 0;
			var num3 = 0;
			foreach (var value in World.HelpList.Values)
			{
				if (value.DiemSoHienTai > num3)
				{
					num3 = value.DiemSoHienTai;
					num2 = value.DangKy_BangPhaiID;
					num = value.DiemSoHienTai;
				}
			}
			if (num > 0)
			{
				var num4 = 0;
				foreach (var value2 in World.HelpList.Values)
				{
					if (value2.DiemSoHienTai == num)
					{
						num4++;
					}
				}
				if (num4 == 1)
				{
					foreach (var value3 in World.HelpList.Values)
					{
						if (value3.DangKy_BangPhaiID == num2)
						{
							DBA.ExeSqlCommand($"UPDATE TBL_XWWL_Guild SET BangPhaiVoHuan=BangPhaiVoHuan+5000,ThanhDanh=ThanhDanh+3,Thang=Thang+1 WHERE ID='{value3.DangKy_BangPhaiID}'");
							var players = World.KiemTra_Ten_NguoiChoi(value3.BangPhaiMonChu);
							if (players != null)
							{
								if (players.NhanVatToaDo_BanDo == 7301)
								{
									try
									{
										var player_Job = players.Player_Job;
										var player_Zx = players.Player_Zx;
										players.UpdateBangPhai_Level(players.CharacterName);
										RxjhClass.SetBangPhaiVinhDuSoLieu(value3.DangKyTenBangPhai, value3.BangPhaiMonChu, value3.DangCap, player_Job, player_Zx, 1);
										players.HeThongNhacNho("Bang phái đã giành chiến thắng, Danh vọng [+1], Võ Huân Đại Môn [+5000], Danh vọng giang hồ [+3]!", 6, "Thiên Cơ Lệnh");
										foreach (var value4 in value3.DanhSachUngVien.Values)
										{
											if (value4.NhanVatToaDo_BanDo == 7301)
											{
												jlwp(value4, bool_0: true);
											}
										}
										World.BangChienThang_ID = value3.DangKy_BangPhaiID;
										World.GuiThongBao("[" + value3.DangKyTenBangPhai + "]取得BangChienThangLoi，ThuDuoc5000BangPhaiVoHuan,所有参战人员ThuDuoc获Thang奖励");
									}
									catch (Exception ex)
									{
										LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "BangChien SetBangPhaiVinhDuSoLieu() error：" + ex);
										LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "BangChien SetBangPhaiVinhDuSoLieu()  error：" + ex);
									}
									continue;
								}
								foreach (var value5 in value3.DanhSachUngVien.Values)
								{
									if (value5.NhanVatToaDo_BanDo == 7301)
									{
										value5.HeThongNhacNho("Bang chiến đã kết thúc, do bang chủ rời khỏi chiến địa, phần thưởng đã bị thiên đình thu hồi!", 6, "Thiên Cơ Lệnh");
									}
								}
								if (World.HelpNameList != null && World.HelpNameList.Count > 0 && World.HelpNameList.ContainsKey(value3.DangKy_BangPhaiID))
								{
									World.HelpNameList.Remove(value3.DangKy_BangPhaiID);
								}
								World.BangChienThang_ID = 0;
								continue;
							}
							foreach (var value6 in value3.DanhSachUngVien.Values)
							{
								if (value6.NhanVatToaDo_BanDo == 7301)
								{
									value6.HeThongNhacNho("Bang chiến đã kết thúc, do bang chủ rời khỏi giang hồ, phần thưởng đã bị thiên đình thu hồi!", 6, "Thiên Cơ Lệnh");
								}
							}
							if (World.HelpNameList != null && World.HelpNameList.Count > 0 && World.HelpNameList.ContainsKey(value3.DangKy_BangPhaiID))
							{
								World.HelpNameList.Remove(value3.DangKy_BangPhaiID);
							}
							World.BangChienThang_ID = 0;
							continue;
						}
						DBA.ExeSqlCommand($"UPDATE TBL_XWWL_Guild SET BangPhaiVoHuan=BangPhaiVoHuan+500,Thua=Thua+1 WHERE ID='{value3.DangKy_BangPhaiID}'");
						foreach (var value7 in value3.DanhSachUngVien.Values)
						{
							jlwp(value7, bool_0: false);
						}
						if (World.HelpNameList != null && World.HelpNameList.Count > 0 && World.HelpNameList.ContainsKey(value3.DangKy_BangPhaiID))
						{
							World.HelpNameList.Remove(value3.DangKy_BangPhaiID);
						}
					}
				}
				else
				{
					World.BangChienThang_ID = 0;
					World.GuiThongBao("BangPhai混战KetThuc,由于最高分相同无BangPhaiThuDuocThangLoi！");
					if (World.HelpNameList != null && World.HelpNameList.Count > 0)
					{
						World.HelpNameList.Clear();
					}
				}
			}
			else
			{
				World.BangChienThang_ID = 0;
				World.GuiThongBao("BangPhai混战KetThuc,无BangPhaiThuDuocThangLoi！");
				if (World.HelpNameList != null && World.HelpNameList.Count > 0)
				{
					World.HelpNameList.Clear();
				}
			}
			if (timer_3 != null)
			{
				timer_3.Enabled = false;
				timer_3.Close();
				timer_3.Dispose();
			}
			Dispose();
		}
		catch
		{
			World.BangChienThang_ID = 0;
			if (World.HelpNameList != null && World.HelpNameList.Count > 0)
			{
				World.HelpNameList.Clear();
			}
			if (timer_3 != null)
			{
				timer_3.Enabled = false;
				timer_3.Close();
				timer_3.Dispose();
			}
			Dispose();
		}
		finally
		{
			Dispose();
		}
	}

	public void jlwp(Players players_0, bool bool_0)
	{
		try
		{
			if (players_0 == null)
			{
				return;
			}
			var bytes = BitConverter.GetBytes(1008000389);
			if (bool_0)
			{
				bytes = BitConverter.GetBytes(1008000388);
			}
			var bytes2 = BitConverter.GetBytes(1);
			var bytes3 = BitConverter.GetBytes(RxjhClass.CreateItemSeries());
			var parcelVacancy = players_0.GetParcelVacancy(players_0);
			if (parcelVacancy != -1)
			{
				players_0.AddItems(bytes3, bytes, parcelVacancy, bytes2, new byte[56]);
				players_0.HeThongNhacNho("Đạo hữu đã nhận được nội công tâm pháp và bảo vật thượng phẩm!", 6, "Thiên Cơ Lệnh");
			}
			if (World.VatPhamThuong_MonChienID != 0 && bool_0)
			{
				var parcelVacancy2 = players_0.GetParcelVacancy(players_0);
				if (parcelVacancy2 != -1)
				{
					players_0.AddItems(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(World.VatPhamThuong_MonChienID), parcelVacancy2, bytes2, new byte[56]);
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "BangChien Phát hành Phan Thuong Vat Pham error：" + ex);
		}
	}

	public void Dispose()
	{
		try
		{
			World.MonChien_ProgressNew = 0;
			World.CoMoRa_HeThong_MonChien = 0;
			if (World.HelpList.Count > 0)
			{
				foreach (var value in World.HelpList.Values)
				{
					foreach (var value2 in value.DanhSachUngVien.Values)
					{
						value2.SystemCountdown(0, 0);
						value2.Mobile(value2.NhanVatToaDo_X, value2.NhanVatToaDo_Y, value2.NhanVatToaDo_Z, 1201, 0);
						value2.SafeMode = 0;
					}
				}
			}
			if (timer_0 != null)
			{
				timer_0.Enabled = false;
				timer_0.Close();
				timer_0.Dispose();
			}
			if (timer_1 != null)
			{
				timer_1.Enabled = false;
				timer_1.Close();
				timer_1.Dispose();
			}
			if (timer_2 != null)
			{
				timer_2.Enabled = false;
				timer_2.Close();
				timer_2.Dispose();
			}
			if (timer_3 != null)
			{
				timer_3.Enabled = false;
				timer_3.Close();
				timer_3.Dispose();
			}
			if (World.HelpList.Count > 0)
			{
				World.HelpList.Clear();
			}
			World.BangChien = null;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "BangChien Dispose() error：" + ex);
		}
		finally
		{
			World.MonChien_ProgressNew = 0;
			World.CoMoRa_HeThong_MonChien = 0;
			if (timer_0 != null)
			{
				timer_0.Enabled = false;
				timer_0.Close();
				timer_0.Dispose();
			}
			if (timer_1 != null)
			{
				timer_1.Enabled = false;
				timer_1.Close();
				timer_1.Dispose();
			}
			if (timer_2 != null)
			{
				timer_2.Enabled = false;
				timer_2.Close();
				timer_2.Dispose();
			}
			if (timer_3 != null)
			{
				timer_3.Enabled = false;
				timer_3.Close();
				timer_3.Dispose();
			}
			if (World.HelpList.Count > 0)
			{
				World.HelpList.Clear();
			}
			World.BangChien = null;
		}
	}
}
