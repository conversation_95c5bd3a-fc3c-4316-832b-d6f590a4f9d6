
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using HeroYulgang.Helpers;
using RxjhServer.Database;

namespace RxjhServer.GroupQuest;

public class GroupQuestEvent
{
    private static GroupQuestEvent _instance;
    private static readonly object _lockObject = new object();

    // Danh sách các quest đang theo dõi
    private Dictionary<int, List<int>> _monsterQuestMap = new Dictionary<int, List<int>>();
    private List<int> _playerKillQuestIds = new List<int>();
    private Dictionary<int, List<int>> _bossQuestMap = new Dictionary<int, List<int>>();
    private Dictionary<int, GroupQuestDefinition> _questDefinitions = new();

    public Dictionary<int, GroupQuestDefinition> QuestDefinitions => _questDefinitions;

    public static GroupQuestEvent Instance
    {
        get
        {
            if (_instance == null)
            {
                lock (_lockObject)
                {
                    _instance ??= new GroupQuestEvent();
                }
            }
            return _instance;
        }
    }

    public GroupQuestEvent()
    {
        // Khởi tạo danh sách các quest
        InitializeQuestMaps();
        LoadAllQuestDefinitions();
    }

    /// <summary>
    /// Load tất cả quest từ database
    /// </summary>
    public void LoadAllQuestDefinitions()
    {
        try
        {
            string query = "SELECT * FROM TBL_GROUP_QUEST WHERE IsActive = 1";
            DataTable dt = DBA.GetDBToDataTable(query);

            if (dt != null && dt.Rows.Count > 0)
            {
                _questDefinitions.Clear();

                foreach (DataRow row in dt.Rows)
                {
                    GroupQuestDefinition quest = new()
                    {
                        ID = Convert.ToInt32(row["ID"]),
                        QuestName = Convert.ToString(row["QuestName"]),
                        QuestDesc = Convert.ToString(row["QuestDesc"]),
                        QuestType = (GroupQuestType)Convert.ToInt32(row["QuestType"]),
                        TargetType = (TargetType)Convert.ToInt32(row["TargetType"]),
                        TargetID = row["TargetID"] != DBNull.Value ? Convert.ToInt32(row["TargetID"]) : (int?)null,
                        TargetCount = Convert.ToInt32(row["TargetCount"]),
                        RequiredLevel = Convert.ToInt32(row["RequiredLevel"]),
                        RewardExp = Convert.ToInt32(row["RewardExp"]),
                        RewardMoney = Convert.ToInt64(row["RewardMoney"]),
                        RewardItem = row["RewardItem"] != DBNull.Value ? Convert.ToInt32(row["RewardItem"]) : (int?)null,
                        RewardItemCount = row["RewardItemCount"] != DBNull.Value ? Convert.ToInt32(row["RewardItemCount"]) : (int?)null,
                        ResetType = (ResetType)Convert.ToInt32(row["ResetType"]),
                        IsActive = Convert.ToBoolean(row["IsActive"]),
                        CreateDate = Convert.ToDateTime(row["CreateDate"])
                    };

                    _questDefinitions[quest.ID] = quest;
                }

                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã load {_questDefinitions.Count} quest từ database");
            }
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi load quest từ database: {ex.Message}");
        }
    }

    /// <summary>
    /// Trạng thái của nhiệm vụ
    /// </summary>
    public enum QuestStatus
    {
        /// <summary>
        /// Đã nhận nhiệm vụ
        /// </summary>
        Accepted = 1,

        /// <summary>
        /// Đang thực hiện
        /// </summary>
        InProgress = 2,

        /// <summary>
        /// Đã hoàn thành
        /// </summary>
        Completed = 3,

        /// <summary>
        /// Đã hủy
        /// </summary>
        Cancelled = 4
    }

    /// <summary>
    /// Khởi tạo danh sách các quest
    /// </summary>
    private void InitializeQuestMaps()
    {
        try
        {
            // Thêm các quest tiêu diệt quái vật
            // Key: Monster ID, Value: List of Quest IDs
            // _monsterQuestMap.Add(1001, new List<int> { 1, 5 }); // Ví dụ: Quái vật ID 1001 liên quan đến quest 1 và 5
            // _monsterQuestMap.Add(1002, new List<int> { 2, 6 });
            // _monsterQuestMap.Add(1003, new List<int> { 3, 7 });

            // // Thêm các quest tiêu diệt người chơi
            // _playerKillQuestIds.Add(4); // Quest ID 4 là quest tiêu diệt người chơi
            // _playerKillQuestIds.Add(8);

            // // Thêm các quest tiêu diệt boss
            // // Key: Boss ID, Value: List of Quest IDs
            // _bossQuestMap.Add(2001, new List<int> { 9 }); // Ví dụ: Boss ID 2001 liên quan đến quest 9
            // _bossQuestMap.Add(2002, new List<int> { 10 });
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khởi tạo danh sách quest: {ex.Message}");
        }
    }

    /// <summary>
    /// Xử lý sự kiện khi người chơi tiêu diệt quái vật
    /// </summary>
    public void RaiseMonsterKilled(NpcClass npc, Players killer)
    {
        try
        {
            if (npc == null || killer == null)
                return;

            int monsterId = npc.FLD_PID;

            ProcessQuestContribution(killer, npc, 1);
            npc.deathHandled = false;
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi xử lý sự kiện tiêu diệt quái vật: {ex.Message}");
        }
    }

    /// <summary>
    /// Xử lý sự kiện khi người chơi tiêu diệt người chơi khác
    /// </summary>
    public void RaisePlayerKilled(Players victim, Players killer, Players[] contributors)
    {
        try
        {
            if (victim == null || killer == null)
                return;

            // Kiểm tra xem có phải là PK giữa 2 phe không
            World.conn.SendPlayerKill(victim, killer, contributors);
            // if (killer.Player_Zx != victim.Player_Zx && killer.Player_Zx > 0 && victim.Player_Zx > 0)
            // {
            //         World.conn.SendPlayerKill(killer, victim, contributors);
            // }

            // Kiểm tra xem có phải là PK giữa 2 guild không
            // if (killer.GuildId > 0 && victim.GuildId > 0 && killer.GuildId != victim.GuildId)
            // {
            //     // Xử lý quest guild
            //     foreach (var questId in _playerKillQuestIds)
            //     {
            //         // Gửi thông tin đến LS
            //         World.conn.SendGroupQuestContribution(1, victim.SessionID, victim.Player_Level, killer.GuildId, killer.SessionID, killer.UserName, killer.Player_Level, 1);
            //     }
            // }
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi xử lý sự kiện tiêu diệt người chơi: {ex.Message}");
        }
    }

    /// <summary>
    /// Xử lý sự kiện khi người chơi tiêu diệt boss
    /// </summary>
    public void RaiseBossKilled(NpcClass npc, Players killer)
    {
        try
        {
            if (npc == null || killer == null)
                return;

            int bossId = npc.FLD_PID;

            // Kiểm tra xem boss có liên quan đến quest nào không
            if (_bossQuestMap.TryGetValue(bossId, out var questIds))
            {
                foreach (var questId in questIds)
                {
                    // Xử lý đóng góp quest
                    ProcessQuestContribution(killer, npc, 1); // Boss thường có giá trị đóng góp cao hơn
                }
            }
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi xử lý sự kiện tiêu diệt boss: {ex.Message}");
        }
    }

    /// <summary>
    /// Xử lý đóng góp quest
    /// </summary>
    private void ProcessQuestContribution(Players player, NpcClass npc, int contributionCount)
    {
        try
        {
            // Xử lý quest guild
            if (player.GuildId > 0)
            {
                // Gửi thông tin đến LS
                World.conn.SendGroupQuestContribution(1, npc.FLD_PID, npc.Level, player.GuildId, player.SessionID, player.CharacterName, player.Player_Level, contributionCount);
            }
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi xử lý đóng góp quest: {ex.Message}");
        }
    }
    // private void ProcessQuestContribution(Players player, int questId, int contributionCount)
    // {
    //     try
    //     {
    //         // Xử lý quest guild
    //         if (player.GuildId > 0)
    //         {
    //             // Gửi thông tin đến LS
    //             World.conn.SendGroupQuestContribution(1, questId, player.GuildId, player.SessionID, player.UserName, contributionCount);
    //         }

    //         // Xử lý quest faction
    //         if (player.Player_Zx > 0)
    //         {
    //             // Gửi thông tin đến LS
    //             World.conn.SendGroupQuestContribution(2, questId, player.Player_Zx, player.SessionID, player.UserName, contributionCount);
    //         }
    //     }
    //     catch (Exception ex)
    //     {
    //         LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi xử lý đóng góp quest: {ex.Message}");
    //     }
    // }
    #region Reward Methods

    /// <summary>
    /// Phát phần thưởng cho người chơi
    /// </summary>
    public bool GiveQuestReward(Players player, int questId)
    {
        try
        {
            //TODO: Phát thưởng cho người chơi

            if (!_questDefinitions.TryGetValue(questId, out var quest))
            {
                return false;
            }
            if (quest.RewardExp > 0)
            {
                player.Player_ExpErience += Convert.ToInt32(quest.RewardExp);
                player.UpdateKinhNghiemVaTraiNghiem();
            }
            if (quest.RewardMoney > 0)
            {
                player.KiemSoatGold_SoLuong(quest.RewardMoney, 1);
                player.UpdateMoneyAndWeight();
            }
            if (quest.RewardItem.HasValue && quest.RewardItemCount.HasValue && quest.RewardItemCount.Value > 0)
            {
                var description = "Ðaòi hiêòp nhâòn ðýõòc vâòt phâÒm thýõÒng Hoaòt ðôòng Bang hôòi. Vui loÌng nhâòn thýõÒng trýõìc 30 ngaÌy hoãòc vâòt phâÒm seÞ biò xoìa!";
                var byteDesc = Encoding.GetEncoding(1252).GetBytes(description);
                var reward = World.CreateAnItem(quest.RewardItem.Value, 1);
                if (reward == null)
                {
                    LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Lỗi khi tạo vật phẩm thưởng nhiệm vụ " + questId);
                    return false;
                }
                World.SendItemMail("[GM]", player.CharacterName, byteDesc, 0, reward, 30);
                player.SendMailCodNotificationByAdmin(player.SessionID);
            }

            player.HeThongNhacNho($"Các hạ nhận được phần thưởng từ nhiệm vụ Bang hội #{questId}!", 10, "Bát Quái Lão Nhân");

            return true;
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi phát phần thưởng quest: {ex.Message}");
            return false;
        }
    }


    /// <summary>
    /// Nhận phần thưởng quest guild
    /// </summary>
    public bool ReceiveGuildQuestReward(Players player, int questId)
    {
        try
        {
            if (player == null || questId <= 0 || player.GuildId <= 0)
            {
                return false;
            }

            var query = $"SELECT * FROM TBL_GROUP_QUEST_CONTRIBUTION WHERE QuestID = {questId} AND PlayerName = '{player.CharacterName}' AND GuildID = {player.GuildId} AND HasReceivedReward = 0";
            var dt = DBA.GetDBToDataTable(query);
            if (dt.Rows.Count <= 0)
            {
                return false;
            }

            var contribute = int.Parse(dt.Rows[0]["ContributionCount"].ToString());

            // Đánh dấu đã nhận thưởng thông qua transmit
            World.conn.MarkRewardReceived(1, questId, player.GuildId, player.SessionID);

            query = $"UPDATE TBL_GROUP_QUEST_CONTRIBUTION SET HasReceivedReward = 1 WHERE QuestID = {questId} AND PlayerName = '{player.CharacterName}' AND GuildID = {player.GuildId}";
            DBA.ExeSqlCommand(query);
            // Phát thưởng
            return GiveQuestReward(player, questId);
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi nhận thưởng quest guild: {ex.Message}");
            return false;
        }
    }

    #endregion
    #region Guild Member

    public int CheckGroupQuestRequirements(Players player, int questId)
    {
        if (questId <= 0)
        {
            // Không tìm thấy thông tin nhiệm vụ
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Không tìm thấy nhiệm vụ {questId}");
            return 12;
        }

        // Giả lập thông tin quest
        // Kiểm tra guild
        if (player.GuildId <= 0)
        {
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Chưa có guild {questId}");
            return 12;
        }

        if (player.Player_Zx <= 0)
        {
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Chưa có phe phái {questId}");
            return 12;
        }

        if (!World.IsGuildMaster(player))
        {
            return 12;
        }
        if (CheckGroupQuestProcess(player.GuildId, questId) > 0)
        {
            //LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Nhiệm vụ đang thực hiện {questId}");
            player.HeThongNhacNho("Vui lòng hủy nhiệm vụ trước khi nhận nhiệm vụ mới!", 10, "Thiên cơ các");
            return 13;
        }

        if (CheckGroupQuestCompleted(player.GuildId, questId)> 0)
        {
            //LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Nhiệm vụ đã hoàn thành {questId}");
            player.HeThongNhacNho("Vui lòng nhận nhiệm vụ vào ngày mai!", 10, "Thiên cơ các");
            return 0x79;
        }

        return 11;
    }


    #endregion

    public int CheckGroupQuestProcess(int guildId, int questId)
    {
        // Get Compelted Quest from db
        try
        {
            var query = $"SELECT * FROM TBL_GUILD_QUEST_PROGRESS " +
                $"WHERE GuildID = {guildId} " +
                $"AND QuestID = {questId} " +
                $"AND Status IN (1,2)" +
                $"AND CompletedTime >= CAST(GETDATE() AS DATE)" +
                $"AND CompletedTime < DATEADD(DAY,1,CAST(GETDATE() AS DATE))";
            var dt = DBA.GetDBToDataTable(query);
            return dt.Rows.Count;
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi kiểm tra quest hoàn thành: {ex.Message}");
            return 0;
        }
    }
    public int CheckGroupQuestCompleted(int guildId, int questId)
    {
        // Get Compelted Quest from db
        try
        {
            var query = $@"SELECT * FROM TBL_GUILD_QUEST_PROGRESS 
               WHERE GuildID = {guildId} 
               AND QuestID = {questId} 
               AND Status = 3 
               AND CompletedTime >= CAST(GETDATE() AS DATE) 
               AND CompletedTime < DATEADD(DAY, 1, CAST(GETDATE() AS DATE))";
            var dt = DBA.GetDBToDataTable(query);
            return dt.Rows.Count;
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi kiểm tra quest hoàn thành: {ex.Message}");
            return 0;
        }
    }


}
