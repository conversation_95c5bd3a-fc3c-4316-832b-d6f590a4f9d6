using System;
using System.Collections.Generic;
using System.Text;
using HeroYulgang.Helpers;
using RxjhServer.HelperTools;

namespace RxjhServer;

public class Event_Dragon_ToDoi_Loai
{
	public static int ALL_SoLieu_Event_Dragon = 1;

	public Players DoiTruong;

	public ThreadSafeDictionary<int, Players> ToDoi_NguoiChoi = new();

	public int ToDoi_ID_Dragon;

	public int ToDoi_Level_Dragon;

	public int ToDoiChuyenChuc_Event;

	public int ToDoi_PhoBan_DoKho;

	public static int Start_Event_Dragon(Event_Dragon_ToDoi_Loai ToDoi_Event)
	{
		ALL_SoLieu_Event_Dragon++;
		ToDoi_Event.ToDoi_ID_Dragon = ALL_SoLieu_Event_Dragon;
		World.ToDoi_Event.Add(ALL_SoLieu_Event_Dragon, ToDoi_Event);
		return ALL_SoLieu_Event_Dragon;
	}

	public static Event_Dragon_ToDoi_Loai DatDuoc_ToDoiEvent_TinTuc(int ToDoi_SoHieu)
	{
		if (World.ToDoi_Event.TryGetValue(ToDoi_SoHieu, out var value))
		{
			return value;
		}
		return null;
	}

	public static void Event_HoaLong(Players player, byte[] data, int legth)
	{
		try
		{
			switch (data[12])
			{
			case 30:
				Start_Event_Dragon(player, data, legth);
				break;
			case 31:
				KiemTra_Event_MucLuc(player, data, legth);
				break;
			case 32:
				KiemTra_Event_TinTuc(player, data, legth);
				break;
			case 33:
				GiaNhap_Event(player, data, legth);
				break;
			case 34:
				Cancel_Event(player, data, legth);
				break;
			case 35:
				Logout_Event(player, data, legth);
				break;
			case 36:
				Kick_Event(player, data, legth);
				break;
			case 37:
				Nhuong_KeyDoiTruong(player, data, legth);
				break;
			case 45:
				this_vent_HoaLong(player, data, legth);
				break;
			case 47:
				break;
			case 48:
				break;
			case 50:
				KiemTra_Monster_DanhThue_DaiSanh(player, data, legth);
				break;
			case 52:
				Money_Map_Cho_TapTrung(player, data, legth);
				break;
			case 53:
				Money_Event(player, data, legth);
				break;
			case 54:
				TiepTuc_Money_Event_ToDoi(player, data, legth);
				break;
			case 58:
				if (World.CoMo_ThiTruongTraoDoiTienXu == 1)
				{
					player.Mobile(10f, 10f, 15f, 1201, 0);
				}
				else
				{
					player.Mobile(420f, 1520f, 15f, 101, 0);
				}
				break;
			case 61:
				MoRa_Event_Rank(player, data, legth);
				break;
			case 62:
				Close_Raid_Panel(player, data, legth);
				break;
			case 38:
			case 39:
			case 40:
			case 41:
			case 42:
			case 43:
			case 44:
			case 46:
			case 49:
			case 51:
			case 55:
			case 56:
			case 57:
			case 59:
			case 60:
				break;
			}
		}
		catch
		{
		}
	}

	public static void TiepTuc_Money_Event_ToDoi(Players player, byte[] data, int legth)
	{
		try
		{
			if (World.讨伐战副本 == null)
			{
				player.HeThongNhacNho("Phó bản chưa mở cửa, đại hiệp hãy chờ thời cơ xuất trận!", 20, "Thiên cơ các");
			}
			else
			{
				if (player.PartyThaoPhatToDoiIdDragon == 0 || X_Boss_Event_FireDragon_Loai.讨伐副本占领者 != player.PartyThaoPhatToDoiIdDragon)
				{
					return;
				}
				var event_Dragon_ToDoi_Loai = DatDuoc_ToDoiEvent_TinTuc(player.PartyThaoPhatToDoiIdDragon);
				if (event_Dragon_ToDoi_Loai == null)
				{
					return;
				}
				if (player.NhanVatToaDo_BanDo != 43001)
				{
					player.Mobile(20f, -600f, 15f, 43001, 0);
					player.发送副本复活剩余次数();
					var dictionary = MapClass.GetnpcTemplate(43001);
					if (dictionary != null)
					{
						NpcClass.Update_NPC_HoiSinh_SoLieu_FireDragon(dictionary, player);
					}
					var time = (int)World.讨伐战副本.讨伐副本进行中结束ThoiGian.Subtract(DateTime.Now).TotalSeconds;
					X_Boss_Event_FireDragon_Loai.进入副本提示(player, time);
				}
				else
				{
					player.HeThongNhacNho("Số lần thảo phạt phó bản đã cạn, cuối tuần đại hiệp hãy trở lại chinh chiến!", 20, "Thiên cơ các");
				}
			}
		}
		catch
		{
		}
	}

	public static void Money_Event(Players player, byte[] data, int legth)
	{
		try
		{
			if (player.PartyThaoPhatToDoiIdDragon == 0)
			{
				return;
			}
			var event_Dragon_ToDoi_Loai = DatDuoc_ToDoiEvent_TinTuc(player.PartyThaoPhatToDoiIdDragon);
			if (event_Dragon_ToDoi_Loai == null || event_Dragon_ToDoi_Loai.DoiTruong != player)
			{
				return;
			}
			if (event_Dragon_ToDoi_Loai.ToDoi_NguoiChoi.Count < 1)
			{
				player.HeThongNhacNho("Nhân số dưới 30 hiệp khách, không thể tiến vào công thành, đại hiệp cần chiêu mộ thêm!", 20, "Thiên cơ các");
			}
			else if (X_Boss_Event_FireDragon_Loai.讨伐副本占领者 == 0)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Thảo phạt phó bản _ 555.");
				X_Boss_Event_FireDragon_Loai.讨伐副本占领者 = player.PartyThaoPhatToDoiIdDragon;
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Thảo phạt phó bản _ 666.");
				foreach (var value in event_Dragon_ToDoi_Loai.ToDoi_NguoiChoi.Values)
				{
					LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Thảo phạt phó bản _ 777.");
					value.ThaoPhat_TinhGop_TonThuong = 0;
					value.副本复活剩余次数 = 10;
					if (value.NhanVatToaDo_BanDo != 43001)
					{
						LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Thảo phạt phó bản _ 888.");
						value.Mobile(20f, -600f, 15f, 43001, 0);
						value.副本剩余次数--;
						value.发送副本复活剩余次数();
						var time = (int)World.讨伐战副本.讨伐副本进行中结束ThoiGian.Subtract(DateTime.Now).TotalSeconds;
						X_Boss_Event_FireDragon_Loai.进入副本提示(value, time);
					}
					else
					{
						value.Mobile(420f, 1500f, 15f, 101, 0);
						player.HeThongNhacNho("Đại hiệp thảo phạt phó bản chưa đủ số lần, cần rèn luyện thêm võ công!", 20, "Thiên cơ các");
					}
				}
				World.讨伐战副本 = new();
			}
			else
			{
				player.HeThongNhacNho("Phó bản thảo phạt đang bị quần hùng chiếm dụng, đại hiệp hãy chờ thời cơ!", 20, "Thiên cơ các");
			}
		}
		catch
		{
		}
	}

	public static void Money_Map_Cho_TapTrung(Players player, byte[] data, int legth)
	{
		try
		{
			if (player.PartyThaoPhatToDoiIdDragon == 0)
			{
				return;
			}
			var event_Dragon_ToDoi_Loai = DatDuoc_ToDoiEvent_TinTuc(player.PartyThaoPhatToDoiIdDragon);
			if (event_Dragon_ToDoi_Loai == null || event_Dragon_ToDoi_Loai.DoiTruong != player)
			{
				return;
			}
			foreach (var value in event_Dragon_ToDoi_Loai.ToDoi_NguoiChoi.Values)
			{
				if (value != event_Dragon_ToDoi_Loai.DoiTruong)
				{
					value.Mobile(415f, -25f, 15f, 1201, 0);
				}
			}
		}
		catch
		{
		}
	}

	public static void Nhuong_KeyDoiTruong(Players player, byte[] data, int legth)
	{
		try
		{
			if (player.PartyThaoPhatToDoiIdDragon == 0)
			{
				return;
			}
			var event_Dragon_ToDoi_Loai = DatDuoc_ToDoiEvent_TinTuc(player.PartyThaoPhatToDoiIdDragon);
			if (event_Dragon_ToDoi_Loai == null || event_Dragon_ToDoi_Loai.DoiTruong != player || event_Dragon_ToDoi_Loai.ToDoi_NguoiChoi.Count <= 1)
			{
				return;
			}
			var array = new byte[15];
			System.Buffer.BlockCopy(data, 138, array, 0, 15);
			var text = Encoding.Default.GetString(array).Replace("\0", string.Empty).Trim();
			if (text.Length > 0)
			{
				var players = World.KiemTra_Ten_NguoiChoi(text);
				if (players != null)
				{
					event_Dragon_ToDoi_Loai.DoiTruong = players;
					更新讨伐队队员信息(player.PartyThaoPhatToDoiIdDragon);
				}
			}
		}
		catch
		{
		}
	}

	public static void Kick_Event(Players player, byte[] data, int legth)
	{
		try
		{
			if (player.PartyThaoPhatToDoiIdDragon == 0)
			{
				return;
			}
			var event_Dragon_ToDoi_Loai = DatDuoc_ToDoiEvent_TinTuc(player.PartyThaoPhatToDoiIdDragon);
			if (event_Dragon_ToDoi_Loai == null || event_Dragon_ToDoi_Loai.DoiTruong != player)
			{
				return;
			}
			var array = new byte[15];
			System.Buffer.BlockCopy(data, 138, array, 0, 15);
			var text = Encoding.Default.GetString(array).Replace("\0", string.Empty).Trim();
			if (text.Length <= 0)
			{
				return;
			}
			var players = World.KiemTra_Ten_NguoiChoi(text);
			if (players != null)
			{
				event_Dragon_ToDoi_Loai.ToDoi_NguoiChoi.Remove(players.SessionID);
				更新讨伐队队员信息(players.PartyThaoPhatToDoiIdDragon);
				players.PartyThaoPhatToDoiIdDragon = 0;
				var array2 = Converter.HexStringToByte("AA55AA0011020105A400080029000000000000000000000000000000000000000000000000000000000000000000000000000A00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
				System.Buffer.BlockCopy(BitConverter.GetBytes(players.SessionID), 0, array2, 4, 2);
				System.Buffer.BlockCopy(BitConverter.GetBytes(event_Dragon_ToDoi_Loai.ToDoi_ID_Dragon), 0, array2, 50, 2);
				if (players.Client != null)
				{
					players.Client.Send_Map_Data(array2, array2.Length);
				}
				var array3 = Converter.HexStringToByte("AA55AA0075050105A40008002A000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
				System.Buffer.BlockCopy(BitConverter.GetBytes(players.SessionID), 0, array3, 4, 2);
				if (players.Client != null)
				{
					players.Client.Send_Map_Data(array3, array3.Length);
				}
			}
		}
		catch
		{
		}
	}

	public static void Close_Raid_Panel(Players player, byte[] data, int legth)
	{
		try
		{
		}
		catch
		{
		}
	}

	public static void MoRa_Event_Rank(Players player, byte[] data, int legth)
	{
		try
		{
			var array = Converter.HexStringToByte("AA55AA0011020105A40008003D0000000100050000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000FFFFFFFF0000000000000000000000005323116100000000108F78C9311500001090000055AA");
			System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 4, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(player.副本剩余次数), 0, array, 18, 4);
			if (player.Client != null)
			{
				player.Client.Send_Map_Data(array, array.Length);
			}
			array = Converter.HexStringToByte("AA55AA0011020105A400080026000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
			System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 4, 2);
			if (player.Client != null)
			{
				player.Client.Send_Map_Data(array, array.Length);
			}
			array = Converter.HexStringToByte("AA55AA0011020105A400080026000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
			System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 4, 2);
			if (player.Client != null)
			{
				player.Client.Send_Map_Data(array, array.Length);
				player.HeThongNhacNho("Đại hiệp hãy xem bí lục sự kiện tại Thiên Cơ Các!", 20, "Thiên cơ các");
			}
			array = Converter.HexStringToByte("AA55AA0011020105A40008001F000000010001000000000000008C0000000000000009000000000000000B000000000000001E000000000000001E0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
			foreach (var value in World.ToDoi_Event.Values)
			{
				System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 4, 2);
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.ToDoi_Level_Dragon), 0, array, 26, 2);
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.ToDoiChuyenChuc_Event), 0, array, 34, 2);
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.ToDoi_ID_Dragon), 0, array, 42, 4);
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.ToDoi_NguoiChoi.Count), 0, array, 50, 4);
				System.Buffer.BlockCopy(BitConverter.GetBytes(30), 0, array, 58, 4);
				if (player.Client != null)
				{
					player.Client.Send_Map_Data(array, array.Length);
				}
			}
		}
		catch
		{
		}
	}

	public static void Logout_Event(Players player, byte[] data, int legth)
	{
		try
		{
			if (player.PartyThaoPhatToDoiIdDragon == 0)
			{
				return;
			}
			var event_Dragon_ToDoi_Loai = DatDuoc_ToDoiEvent_TinTuc(player.PartyThaoPhatToDoiIdDragon);
			if (event_Dragon_ToDoi_Loai == null)
			{
				return;
			}
			event_Dragon_ToDoi_Loai.ToDoi_NguoiChoi.Remove(player.SessionID);
			if (event_Dragon_ToDoi_Loai.DoiTruong == player && event_Dragon_ToDoi_Loai.ToDoi_NguoiChoi.Count > 0)
			{
				using var enumerator = event_Dragon_ToDoi_Loai.ToDoi_NguoiChoi.Values.GetEnumerator();
				if (enumerator.MoveNext())
				{
					var current = enumerator.Current;
					event_Dragon_ToDoi_Loai.DoiTruong = current;
				}
			}
			更新讨伐队队员信息(player.PartyThaoPhatToDoiIdDragon);
			player.PartyThaoPhatToDoiIdDragon = 0;
			if (event_Dragon_ToDoi_Loai.ToDoi_NguoiChoi.Count < 1)
			{
				World.ToDoi_Event.Remove(event_Dragon_ToDoi_Loai.ToDoi_ID_Dragon);
			}
			var array = Converter.HexStringToByte("AA55AA0011020105A400080029000000000000000000000000000000000000000000000000000000000000000000000000000A00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
			System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 4, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(event_Dragon_ToDoi_Loai.ToDoi_ID_Dragon), 0, array, 50, 2);
			if (player.Client != null)
			{
				player.Client.Send_Map_Data(array, array.Length);
			}
			var array2 = Converter.HexStringToByte("AA55AA0075050105A40008002A000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
			System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array2, 4, 2);
			if (player.Client != null)
			{
				player.Client.Send_Map_Data(array2, array2.Length);
			}
		}
		catch
		{
		}
	}

	public static void Start_Event_Dragon(Players player, byte[] data, int legth)
	{
		try
		{
			int toDoi_Level_Dragon = data[26];
			int toDoiChuyenChuc_Event = data[34];
			if (player.PartyThaoPhatToDoiIdDragon == 0)
			{
				Event_Dragon_ToDoi_Loai event_Dragon_ToDoi_Loai = new();
				event_Dragon_ToDoi_Loai.DoiTruong = player;
				event_Dragon_ToDoi_Loai.ToDoi_NguoiChoi.Add(player.SessionID, player);
				event_Dragon_ToDoi_Loai.ToDoi_Level_Dragon = toDoi_Level_Dragon;
				event_Dragon_ToDoi_Loai.ToDoiChuyenChuc_Event = toDoiChuyenChuc_Event;
				Start_Event_Dragon(event_Dragon_ToDoi_Loai);
				player.PartyThaoPhatToDoiIdDragon = event_Dragon_ToDoi_Loai.ToDoi_ID_Dragon;
				var array = Converter.HexStringToByte("AA55AA0075050105A40008001E000100010001000000000000008C0000000000000009000000000000000B000000000000001400000000000000320000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000AA00000066B4A66DE614000000000000AA00000006F5A66DE61400001E00000055AA");
				System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 4, 2);
				System.Buffer.BlockCopy(BitConverter.GetBytes(event_Dragon_ToDoi_Loai.ToDoi_Level_Dragon), 0, array, 26, 2);
				System.Buffer.BlockCopy(BitConverter.GetBytes(event_Dragon_ToDoi_Loai.ToDoiChuyenChuc_Event), 0, array, 34, 2);
				System.Buffer.BlockCopy(BitConverter.GetBytes(event_Dragon_ToDoi_Loai.ToDoi_ID_Dragon), 0, array, 42, 4);
				System.Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 50, 4);
				System.Buffer.BlockCopy(BitConverter.GetBytes(World.讨伐副本最多人数), 0, array, 58, 4);
				if (player.Client != null)
				{
					player.Client.Send_Map_Data(array, array.Length);
				}
			}
			else
			{
				player.HeThongNhacNho("Đại hiệp chưa đủ số lần thảo phạt, không thể mở phó bản chinh chiến!", 20, "Thiên cơ các");
			}
		}
		catch
		{
		}
	}

	public static void KiemTra_Event_MucLuc(Players player, byte[] data, int legth)
	{
		try
		{
			var array = Converter.HexStringToByte("AA55AA0011020105A400080026000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
			System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 4, 2);
			if (player.Client != null)
			{
				player.Client.Send_Map_Data(array, array.Length);
			}
			foreach (var value in World.ToDoi_Event.Values)
			{
				array = Converter.HexStringToByte("AA55AA0011020105A40008001F000000010001000000000000008C0000000000000009000000000000000B000000000000001E000000000000001E0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
				System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 4, 2);
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.ToDoi_Level_Dragon), 0, array, 26, 2);
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.ToDoiChuyenChuc_Event), 0, array, 34, 2);
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.ToDoi_ID_Dragon), 0, array, 42, 4);
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.ToDoi_NguoiChoi.Count), 0, array, 50, 4);
				System.Buffer.BlockCopy(BitConverter.GetBytes(World.讨伐副本最多人数), 0, array, 58, 4);
				if (player.Client != null)
				{
					player.Client.Send_Map_Data(array, array.Length);
				}
			}
		}
		catch
		{
		}
	}

	public static void KiemTra_Event_TinTuc(Players player, byte[] data, int legth)
	{
		try
		{
			int toDoi_SoHieu = data[42];
			var event_Dragon_ToDoi_Loai = DatDuoc_ToDoiEvent_TinTuc(toDoi_SoHieu);
			if (event_Dragon_ToDoi_Loai == null)
			{
				return;
			}
			var array = Converter.HexStringToByte("AA55AA0011020105A400080029000000000000000000000000000000000000000000000000000000000000000000000000000A00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
			System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 4, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(event_Dragon_ToDoi_Loai.ToDoi_ID_Dragon), 0, array, 50, 2);
			if (player.Client != null)
			{
				player.Client.Send_Map_Data(array, array.Length);
			}
			var doiTruong = event_Dragon_ToDoi_Loai.DoiTruong;
			array = Converter.HexStringToByte("AA55AA0075050105A4000800200001000100650000000000000004000000000000009600000000000000020000000000000005000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000C1000000000000000000000000000000000000000000000000000000000000000000000055AA");
			System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 4, 2);
			if (player == event_Dragon_ToDoi_Loai.DoiTruong)
			{
				System.Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 14, 2);
			}
			else
			{
				System.Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array, 14, 2);
			}
			System.Buffer.BlockCopy(BitConverter.GetBytes(doiTruong.副本剩余次数), 0, array, 18, 4);
			System.Buffer.BlockCopy(BitConverter.GetBytes(doiTruong.Player_Job), 0, array, 26, 4);
			System.Buffer.BlockCopy(BitConverter.GetBytes(doiTruong.Player_Level), 0, array, 34, 2);
			if (doiTruong == event_Dragon_ToDoi_Loai.DoiTruong)
			{
				System.Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 42, 2);
			}
			else
			{
				System.Buffer.BlockCopy(BitConverter.GetBytes(2), 0, array, 42, 2);
			}
			System.Buffer.BlockCopy(BitConverter.GetBytes(event_Dragon_ToDoi_Loai.ToDoi_ID_Dragon), 0, array, 50, 2);
			var bytes = Encoding.Default.GetBytes(doiTruong.CharacterName);
			System.Buffer.BlockCopy(bytes, 0, array, 138, bytes.Length);
			if (player.Client != null)
			{
				player.Client.Send_Map_Data(array, array.Length);
			}
			foreach (var value in event_Dragon_ToDoi_Loai.ToDoi_NguoiChoi.Values)
			{
				if (value != event_Dragon_ToDoi_Loai.DoiTruong)
				{
					array = Converter.HexStringToByte("AA55AA0075050105A4000800200001000100650000000000000004000000000000009600000000000000020000000000000005000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000C1000000000000000000000000000000000000000000000000000000000000000000000055AA");
					System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 4, 2);
					if (player == event_Dragon_ToDoi_Loai.DoiTruong)
					{
						System.Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 14, 2);
					}
					else
					{
						System.Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array, 14, 2);
					}
					System.Buffer.BlockCopy(BitConverter.GetBytes(value.副本剩余次数), 0, array, 18, 4);
					System.Buffer.BlockCopy(BitConverter.GetBytes(value.Player_Job), 0, array, 26, 4);
					System.Buffer.BlockCopy(BitConverter.GetBytes(value.Player_Level), 0, array, 34, 2);
					if (value == event_Dragon_ToDoi_Loai.DoiTruong)
					{
						System.Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 42, 2);
					}
					else
					{
						System.Buffer.BlockCopy(BitConverter.GetBytes(2), 0, array, 42, 2);
					}
					System.Buffer.BlockCopy(BitConverter.GetBytes(event_Dragon_ToDoi_Loai.ToDoi_ID_Dragon), 0, array, 50, 2);
					var bytes2 = Encoding.Default.GetBytes(value.CharacterName);
					System.Buffer.BlockCopy(bytes2, 0, array, 138, bytes2.Length);
					if (player.Client != null)
					{
						player.Client.Send_Map_Data(array, array.Length);
					}
				}
			}
		}
		catch
		{
		}
	}

	public static void GiaNhap_Event(Players player, byte[] data, int legth)
	{
		try
		{
			int num = data[42];
			if (player.PartyThaoPhatToDoiIdDragon == 0)
			{
				var event_Dragon_ToDoi_Loai = DatDuoc_ToDoiEvent_TinTuc(num);
				if (event_Dragon_ToDoi_Loai != null)
				{
					player.PartyThaoPhatToDoiIdDragon = num;
					event_Dragon_ToDoi_Loai.ToDoi_NguoiChoi.Add(player.SessionID, player);
					更新讨伐队队员信息(num);
				}
			}
		}
		catch
		{
		}
	}

	public static void Cancel_Event(Players player, byte[] data, int legth)
	{
		try
		{
			if (player.PartyThaoPhatToDoiIdDragon == 0)
			{
				return;
			}
			var event_Dragon_ToDoi_Loai = DatDuoc_ToDoiEvent_TinTuc(player.PartyThaoPhatToDoiIdDragon);
			if (event_Dragon_ToDoi_Loai != null)
			{
				event_Dragon_ToDoi_Loai.ToDoi_NguoiChoi.Remove(player.SessionID);
				更新讨伐队队员信息(player.PartyThaoPhatToDoiIdDragon);
				player.PartyThaoPhatToDoiIdDragon = 0;
				if (event_Dragon_ToDoi_Loai.ToDoi_NguoiChoi.Count < 1)
				{
					World.ToDoi_Event.Remove(event_Dragon_ToDoi_Loai.ToDoi_ID_Dragon);
				}
				var array = Converter.HexStringToByte("AA55AA0011020105A400080029000000000000000000000000000000000000000000000000000000000000000000000000000A00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
				System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 4, 2);
				System.Buffer.BlockCopy(BitConverter.GetBytes(event_Dragon_ToDoi_Loai.ToDoi_ID_Dragon), 0, array, 50, 2);
				if (player.Client != null)
				{
					player.Client.Send_Map_Data(array, array.Length);
				}
				var array2 = Converter.HexStringToByte("AA55AA0075050105A40008002A000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
				System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array2, 4, 2);
				if (player.Client != null)
				{
					player.Client.Send_Map_Data(array2, array2.Length);
				}
			}
		}
		catch
		{
		}
	}

	public static void this_vent_HoaLong(Players player, byte[] data, int legth)
	{
		try
		{
			var array = Converter.HexStringToByte("AA55AA0011020105A40008002D000000222800000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
			System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 4, 2);
			if (player.Client != null)
			{
				player.Client.Send_Map_Data(array, array.Length);
			}
		}
		catch
		{
		}
	}

	public static void KiemTra_Monster_DanhThue_DaiSanh(Players player, byte[] data, int legth)
	{
		try
		{
		}
		catch
		{
		}
	}

	public static void Start_Event_Dragon111(Players player, byte[] data, int legth)
	{
		try
		{
		}
		catch
		{
		}
	}

	public static void 查看讨伐队成员()
	{
	}

	public static void 更新讨伐队队员信息(int Party_ThaoPhat_ToDoi_ID_Dragon)
	{
		try
		{
			var event_Dragon_ToDoi_Loai = DatDuoc_ToDoiEvent_TinTuc(Party_ThaoPhat_ToDoi_ID_Dragon);
			if (event_Dragon_ToDoi_Loai == null)
			{
				return;
			}
			foreach (var value in event_Dragon_ToDoi_Loai.ToDoi_NguoiChoi.Values)
			{
				var array = Converter.HexStringToByte("AA55AA0011020105A400080029000000000000000000000000000000000000000000000000000000000000000000000000000A00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.SessionID), 0, array, 4, 2);
				System.Buffer.BlockCopy(BitConverter.GetBytes(event_Dragon_ToDoi_Loai.ToDoi_ID_Dragon), 0, array, 50, 2);
				if (value.Client != null)
				{
					value.Client.Send_Map_Data(array, array.Length);
				}
				foreach (var value2 in event_Dragon_ToDoi_Loai.ToDoi_NguoiChoi.Values)
				{
					array = Converter.HexStringToByte("AA55AA0011020105A4000800200000000100030000000000000005000000000000009600000000000000010000000000000002000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000D2000000000000000000000000000000000000000000000000000000000000000000000055AA");
					System.Buffer.BlockCopy(BitConverter.GetBytes(value.SessionID), 0, array, 4, 2);
					System.Buffer.BlockCopy(BitConverter.GetBytes(value2.副本剩余次数), 0, array, 18, 4);
					System.Buffer.BlockCopy(BitConverter.GetBytes(value2.Player_Job), 0, array, 26, 4);
					System.Buffer.BlockCopy(BitConverter.GetBytes(value2.Player_Level), 0, array, 34, 2);
					if (value2 == event_Dragon_ToDoi_Loai.DoiTruong)
					{
						System.Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 42, 2);
					}
					else
					{
						System.Buffer.BlockCopy(BitConverter.GetBytes(2), 0, array, 42, 2);
					}
					System.Buffer.BlockCopy(BitConverter.GetBytes(event_Dragon_ToDoi_Loai.ToDoi_ID_Dragon), 0, array, 50, 2);
					var bytes = Encoding.Default.GetBytes(value2.CharacterName);
					System.Buffer.BlockCopy(bytes, 0, array, 138, bytes.Length);
					if (value.Client != null)
					{
						value.Client.Send_Map_Data(array, array.Length);
					}
				}
			}
		}
		catch
		{
		}
	}
}
