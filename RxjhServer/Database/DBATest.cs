using System;
using System.Data;
using System.Threading.Tasks;
using Microsoft.Data.SqlClient;
using HeroYulgang.Core;
using HeroYulgang.Services;

namespace RxjhServer.Database
{
    /// <summary>
    /// Test class để kiểm tra DBA.cs hoạt động đúng với DatabaseManager singleton
    /// </summary>
    public static class DBATest
    {
        /// <summary>
        /// Kiểm tra kết nối database và các phương thức DBA
        /// </summary>
        public static async Task RunTestsAsync()
        {
            try
            {
                Logger.Instance.Info("Bắt đầu kiểm tra DBA...");

                // Đảm bảo DatabaseManager được khởi tạo
                if (!HeroYulgang.Core.DatabaseManager.Instance.Initialize())
                {
                    Logger.Instance.Error("Không thể khởi tạo DatabaseManager");
                    return;
                }

                Logger.Instance.Info("DatabaseManager đã được khởi tạo thành công");

                // Test 1: <PERSON>ể<PERSON> tra GetDBToDataTable
                await TestGetDBToDataTable();

                // Test 2: Kiểm tra GetDBValue_3
                await TestGetDBValue();

                // Test 3: Kiểm tra ExeSqlCommand
                await TestExeSqlCommand();

                // Test 4: Kiểm tra các phương thức async
                await TestAsyncMethods();

                // Test 5: Kiểm tra stored procedures
                await TestStoredProcedures();

                Logger.Instance.Info("Tất cả các test DBA đã hoàn thành thành công");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi chạy test DBA: {ex.Message}");
            }
        }

        private static async Task TestGetDBToDataTable()
        {
            try
            {
                Logger.Instance.Info("Test GetDBToDataTable...");

                // Test với GameDb
                var result = DBA.GetDBToDataTable("SELECT TOP 1 1 as TestValue", "GameDb");
                if (result != null && result.Rows.Count > 0)
                {
                    Logger.Instance.Info("✓ GetDBToDataTable GameDb hoạt động đúng");
                }
                else
                {
                    Logger.Instance.Warning("⚠ GetDBToDataTable GameDb trả về kết quả rỗng");
                }

                // Test với AccountDb
                var result2 = DBA.GetDBToDataTable("SELECT TOP 1 1 as TestValue", "AccountDb");
                if (result2 != null && result2.Rows.Count > 0)
                {
                    Logger.Instance.Info("✓ GetDBToDataTable AccountDb hoạt động đúng");
                }
                else
                {
                    Logger.Instance.Warning("⚠ GetDBToDataTable AccountDb trả về kết quả rỗng");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Test GetDBToDataTable thất bại: {ex.Message}");
            }
        }

        private static async Task TestGetDBValue()
        {
            try
            {
                Logger.Instance.Info("Test GetDBValue_3...");

                var result = DBA.GetDBValue_3("SELECT 123", "GameDb");
                if (result != null && result.ToString() == "123")
                {
                    Logger.Instance.Info("✓ GetDBValue_3 hoạt động đúng");
                }
                else
                {
                    Logger.Instance.Warning($"⚠ GetDBValue_3 trả về kết quả không mong đợi: {result}");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Test GetDBValue_3 thất bại: {ex.Message}");
            }
        }

        private static async Task TestExeSqlCommand()
        {
            try
            {
                Logger.Instance.Info("Test ExeSqlCommand...");

                // Test với một câu lệnh SELECT đơn giản (không thay đổi dữ liệu)
                var result = DBA.ExeSqlCommand("SELECT 1", "GameDb");
                Logger.Instance.Info($"✓ ExeSqlCommand hoạt động đúng, kết quả: {result}");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Test ExeSqlCommand thất bại: {ex.Message}");
            }
        }

        private static async Task TestAsyncMethods()
        {
            try
            {
                Logger.Instance.Info("Test Async Methods...");

                // Test GetDBToDataTableAsync
                var result1 = await DBA.GetDBToDataTableAsync("SELECT TOP 1 1 as TestValue", "GameDb");
                if (result1 != null && result1.Rows.Count > 0)
                {
                    Logger.Instance.Info("✓ GetDBToDataTableAsync hoạt động đúng");
                }

                // Test GetDBValue_3Async
                var result2 = await DBA.GetDBValue_3Async("SELECT 456", "GameDb");
                if (result2 != null && result2.ToString() == "456")
                {
                    Logger.Instance.Info("✓ GetDBValue_3Async hoạt động đúng");
                }

                // Test ExeSqlCommandAsync
                var result3 = await DBA.ExeSqlCommandAsync("SELECT 1", "GameDb");
                Logger.Instance.Info($"✓ ExeSqlCommandAsync hoạt động đúng, kết quả: {result3}");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Test Async Methods thất bại: {ex.Message}");
            }
        }

        private static async Task TestStoredProcedures()
        {
            try
            {
                Logger.Instance.Info("Test Stored Procedures...");

                // Test ExeStoredProcedure với một stored procedure đơn giản
                // Sử dụng sp_helpdb để test (có sẵn trong SQL Server)
                try
                {
                    var result1 = DBA.ExeStoredProcedure("sp_helpdb", "GameDb");
                    Logger.Instance.Info($"✓ ExeStoredProcedure hoạt động đúng, kết quả: {result1}");
                }
                catch (Exception ex)
                {
                    Logger.Instance.Warning($"⚠ ExeStoredProcedure test bị skip: {ex.Message}");
                }

                // Test ExeStoredProcedureAsync
                try
                {
                    var result2 = await DBA.ExeStoredProcedureAsync("sp_helpdb", "GameDb");
                    Logger.Instance.Info($"✓ ExeStoredProcedureAsync hoạt động đúng, kết quả: {result2}");
                }
                catch (Exception ex)
                {
                    Logger.Instance.Warning($"⚠ ExeStoredProcedureAsync test bị skip: {ex.Message}");
                }

                // Test GetStoredProcedureToDataTable
                try
                {
                    var result3 = DBA.GetStoredProcedureToDataTable("sp_helpdb", null, "GameDb");
                    if (result3 != null)
                    {
                        Logger.Instance.Info($"✓ GetStoredProcedureToDataTable hoạt động đúng, rows: {result3.Rows.Count}");
                    }
                }
                catch (Exception ex)
                {
                    Logger.Instance.Warning($"⚠ GetStoredProcedureToDataTable test bị skip: {ex.Message}");
                }

                Logger.Instance.Info("✓ Test Stored Procedures hoàn thành");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Test Stored Procedures thất bại: {ex.Message}");
            }
        }

        /// <summary>
        /// Test đồng thời nhiều kết nối để kiểm tra thread safety
        /// </summary>
        public static async Task TestConcurrentConnections()
        {
            try
            {
                Logger.Instance.Info("Test Concurrent Connections...");

                var tasks = new Task[10];
                for (int i = 0; i < 10; i++)
                {
                    int taskId = i;
                    tasks[i] = Task.Run(async () =>
                    {
                        try
                        {
                            var result = DBA.GetDBValue_3($"SELECT {taskId + 1}", "GameDb");
                            Logger.Instance.Info($"Task {taskId}: ✓ Kết quả = {result}");
                        }
                        catch (Exception ex)
                        {
                            Logger.Instance.Error($"Task {taskId}: ✗ Lỗi = {ex.Message}");
                        }
                    });
                }

                await Task.WhenAll(tasks);
                Logger.Instance.Info("✓ Test Concurrent Connections hoàn thành");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Test Concurrent Connections thất bại: {ex.Message}");
            }
        }
    }
}
