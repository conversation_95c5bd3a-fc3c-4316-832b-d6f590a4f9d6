using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Text;
using System.Threading.Tasks;
using HeroYulgang.Helpers;
using Microsoft.Data.SqlClient;
using RxjhServer;
using RxjhServer.Database;
using RxjhServer.HelperTools;
using RxjhServer.Network;
using static RxjhServer.Players;

namespace RxjhServer
{
    /// <summary>
    /// Hệ thống quản lý mail và gửi vật phẩm giữa các nhân vật
    /// </summary>
    public class MailSystem
    {
        // Các trạng thái của mail
        public enum MailStatus
        {
            Error = -1,
            Sent = 3,
            Rejected = 4,
            Paid = 5,
            AdminMail = 6
        }

        private readonly Players _player;
        private const int MAX_MAIL_COUNT = 30;
        private const int DESCRIPTION_LENGTH = 200;
        private const int offset = 2;

        public MailSystem(Players player)
        {
            _player = player;
        }

        #region Public Methods

        /// <summary>
        /// <PERSON><PERSON>u cầu danh sách mail (phiên bản test)
        /// </summary>
        public void RequestMailListTest(byte[] data)
        {
            var dbTable = FetchMailForCharacter(_player.CharacterName);
            var dbTable2 = GetMailSendByCharacter(_player.CharacterName);
            var dbTable3 = FetchAdminMailForCharacter(_player.CharacterName);
            //LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Number of mail " + dbTable.Rows.Count);
            //LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Number of mail 2" + dbTable2.Rows.Count);
            //LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Number of mail 3" + dbTable3.Rows.Count);

            var totalMail = dbTable.Rows.Count + dbTable2.Rows.Count + dbTable3.Rows.Count;
            if (totalMail <= 0)
            {
                SendEmptyMailResponse();
                return;
            }

            SendMailListResponse(dbTable, dbTable2, dbTable3);
        }

        /// <summary>
        /// Yêu cầu danh sách mail
        /// </summary>
        public void RequestMailList(byte[] data)
        {
            var dbTable = FetchMailForCharacter(_player.CharacterName);
            var dbTable2 = GetMailSendByCharacter(_player.CharacterName);
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Number of mail " + dbTable.Rows.Count);
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Number of mail 2" + dbTable2.Rows.Count);

            var totalMail = dbTable.Rows.Count + dbTable2.Rows.Count;
            if (totalMail <= 0)
            {
                SendEmptyMailResponse();
                return;
            }

            SendMailListResponse(dbTable, dbTable2, null);
        }

        /// <summary>
        /// Gửi mail kèm vật phẩm
        /// </summary>
        public void SendMailCod(byte[] data)
        {
            var offset = 2;
            try
            {
                var sender = _player.CharacterName;
                var receiverByte = new byte[14];
                System.Buffer.BlockCopy(data, 0x1F - offset, receiverByte, 0, receiverByte.Length);
                var receiver = Encoding.GetEncoding(World.Language_Charset).GetString(receiverByte).Trim().Replace("\0", String.Empty);
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Re " + receiver);
                if (receiver.Length < 4 || !CheckPlayerExists(receiver))
                {
                    _player.HeThongNhacNho("Nhân vật nhận hàng không tồn tại", 7, "Mail");
                    return;
                }
                
                int descLength = data[0x2e - offset];
                var descriptionByte = new byte[DESCRIPTION_LENGTH];
                System.Buffer.BlockCopy(data, 0x30 - offset, descriptionByte, 0, DESCRIPTION_LENGTH);
                double price = BitConverter.ToInt64(data, 0x160 - offset);

                var itemByte = new byte[World.Item_Byte_Length_92];
                System.Buffer.BlockCopy(data, 0x104 - offset, itemByte, 0, World.Item_Byte_Length_92);
                
                var item = new X_Vat_Pham_Loai();
                item.VatPham_byte = itemByte;
                
                var itemPos = _player.FindItemPositionByGlobalID(BitConverter.ToInt64(itemByte, 0));
                if (itemPos == -1)
                {
                    _player.HeThongNhacNho("Không tìm thấy vật phẩm gửi đi trong túi đồ!", 7, "TLE");
                    return;
                }
                
                int amount = data[0x114 - offset];
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "SL " + amount + " " + _player.Item_In_Bag[itemPos].GetVatPhamSoLuong);
                var amountLeft = (_player.Item_In_Bag[itemPos].GetVatPhamSoLuong - amount);
                if (amountLeft < 0)
                {
                    _player.HeThongNhacNho("Bạn không thể gửi nhiều hơn số lượng mình có!", 7, "TLE");
                    return;
                }

                // Sử dụng item từ server để chống bug
                item.VatPham_byte = _player.Item_In_Bag[itemPos].VatPham_byte;
                // Lưu số lượng gửi lên
                item.VatPhamSoLuong = BitConverter.GetBytes(amount);
                
                if (SaveMailToDatabase(sender, receiver, item, price, descriptionByte))
                {
                    // Cập nhật số lượng vật phẩm còn lại trong túi đồ
                    UpdateInventoryAfterSendingItem(itemPos, amountLeft);
                    
                    // Thông báo cho người nhận nếu đang online
                    NotifyReceiverIfOnline(receiver, sender);
                    
                    _player.HeThongNhacNho("Gửi hàng thành công cho [" + receiver + "]", 7, "TLE");
                }
                else
                {
                    _player.HeThongNhacNho("Có lỗi xảy ra với máy chủ! Vui lòng thử lại sau", 7, "TLE");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "SendMailCod Error " + ex.Message);
            }
        }

        /// <summary>
        /// Chấp nhận mail và nhận vật phẩm
        /// </summary>
         public void AcceptMailCod(byte[] data)
        {
            var offset = 2;
            try
            {
                int mailId = data[12 - offset];
                int type = data[16 - offset];
                var dbTable = FindMailByID(mailId,  type);
                if (dbTable == null || dbTable.Rows.Count <= 0)
                {
                    _player.HeThongNhacNho("Không tìm thấy vật phẩm hoặc vật phẩm đã được nhận rồi!", 7, "TLE");
                    return;
                }

                var price = (long)dbTable.Rows[0]["PRICE"];
                var emptySlot = _player.GetParcelVacancy(_player);
                var mailItem = new X_Vat_Pham_Loai();
                var mailItemByte = (byte[])dbTable.Rows[0]["ITEMBYTE"];
            
                switch (type)
                {
                    case 4:
                        if (emptySlot == -1)
                        {
                            _player.HeThongNhacNho("Thùng đồ không đủ chỗ trống!!!", 7, "TLE");
                            return;
                        }

                        mailItem.VatPham_byte = (byte[])dbTable.Rows[0]["ITEMBYTE"];
                        if (mailItem.GetVatPhamSoLuong > 1)
                        {
                            var newId = BitConverter.GetBytes(RxjhClass.CreateItemSeries());
                            System.Buffer.BlockCopy(newId, 0, mailItemByte, 0, 8);
                        }

                        UpdateMailStatusByID(mailId, 6, true);
                        _player.AddItems(mailItem.ItemGlobal_ID, mailItem.VatPham_ID, emptySlot, mailItem.VatPhamSoLuong,
                            mailItem.VatPham_ThuocTinh);
                        if (_player.CharacterBeast != null)
                        {
                            _player.UpdateTheEquipmentBasketPackageOfTheSpiritBeastSInitialStory();
                            _player.UpdateTheWeightOfTheBeast();
                        }
                        break;
                    case 5:
                        MailSystem_Currency_Update(price, CurrencyOperation.Increase);
                        UpdateMailStatusByID(mailId, 6, true);
                        break;
                        case 3:
                        if (_player.Player_Money <= price)
                        {
                            _player.HeThongNhacNho("Bạn không đủ ngân lượng để nhận vật phẩm này!", 7, "TLE");
                            return;
                        }

                        if (emptySlot == -1)
                        {
                            _player.HeThongNhacNho("Thùng đồ không đủ chỗ trống!!!", 7, "TLE");
                            return;
                        }

                        mailItem.VatPham_byte = (byte[])dbTable.Rows[0]["ITEMBYTE"];
                        if (mailItem.GetVatPhamSoLuong > 1)
                        {
                            var newId = BitConverter.GetBytes(RxjhClass.CreateItemSeries());
                            System.Buffer.BlockCopy(newId, 0, mailItemByte, 0, 8);
                        }
                        MailSystem_Currency_Update(price, 0);
                        UpdateMailStatusByID(mailId, 5, true);
                        // Item_In_Bag[emptySlot].VatPham_byte = mailItemByte;
                        _player.AddItems(mailItem.ItemGlobal_ID, mailItem.VatPham_ID, emptySlot, mailItem.VatPhamSoLuong,
                            mailItem.VatPham_ThuocTinh);

                        if (_player.CharacterBeast != null)
                        {
                            _player.UpdateTheEquipmentBasketPackageOfTheSpiritBeastSInitialStory();
                            _player.UpdateTheWeightOfTheBeast();
                        }

                        var sender = dbTable.Rows[0]["SENDER"].ToString();
                        foreach (var character in World.allConnectedChars.Values)
                        {
                            if (character == null)
                            {
                                break;
                            }
                            if (character.CharacterName == sender)
                            {
                                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Found Character " + sender);
                                character.HeThongNhacNho(
                                    "Bưu kiện của bạn đã được giao thành công! Vui lòng tới [Bát Quái Lão Nhân] ở [Huyền Bột Phái] để nhận tiền COD",
                                    7, "TLE");
                                SendMailCodNotification(character);
                                //SendNewCodIdNotification(character,123);
                                break;
                            }
                        }
                        break;
                }

                SendingClass packetData = new();
                packetData.Write1(0);
                packetData.Write1(3);
                packetData.Write1(0);
                packetData.Write4(mailId);
                _player.Client?.SendPak(packetData, 10017, _player.SessionID,true);
            }
            catch (Exception e)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "AcceptMailCodError " + e.Message);
            }
        }
        public void AcceptMailCodOld(byte[] data)
        {
            try
            {
                int id = BitConverter.ToInt32(data, 12-offset);
                int type = data[16 - offset];
                var dbTable = FindMailByID(id, (int)type);
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "MailId " + id);
                
                if (dbTable.Rows.Count <= 0)
                {
                    _player.HeThongNhacNho("Không tìm thấy mail này hoặc mail đã được xử lý!", 7, "TLE");
                    return;
                }
                
                var row = dbTable.Rows[0];
                var price = (long)row["PRICE"];
                
                if (_player.Player_Money < price)
                {
                    _player.HeThongNhacNho("Bạn không đủ tiền để nhận hàng!", 7, "TLE");
                    return;
                }
                
                var mailCod = (byte[])row["ITEMBYTE"];
                var marketItem = new X_Vat_Pham_Loai();
                marketItem.VatPham_byte = mailCod;
                
                // Kiểm tra túi đồ có đủ chỗ không
                var emptySlot = _player.GetParcelVacancyPosition();
                if (emptySlot == -1)
                {
                    _player.HeThongNhacNho("Túi đồ của bạn đã đầy!", 7, "TLE");
                    return;
                }
                
                // Cập nhật trạng thái mail
                if (UpdateMailStatusByID(id, (int)MailStatus.Paid, true))
                {
                    // Trừ tiền người nhận
                    _player.MailSystem_Currency_Update(price, CurrencyOperation.Decrease);
                    
                    // Thêm vật phẩm vào túi đồ
                    _player.Item_In_Bag[emptySlot].VatPham_byte = marketItem.VatPham_byte;
                    _player.Init_Item_In_Bag();
                    
                    // Thông báo cho người gửi nếu đang online
                    var sender = row["SENDER"].ToString();
                    foreach (var character in World.allConnectedChars.Values)
                    {
                        if (character.CharacterName == sender)
                        {
                            character.HeThongNhacNho($"[{_player.CharacterName}] đã nhận bưu phẩm của bạn!", 7, "TLE");
                            //  SendNewCodIdNotification(character, 123);
                            SendMailCodNotification(character);
                            break;
                        }
                    }
                    
                    _player.HeThongNhacNho("Nhận hàng thành công!", 7, "TLE");
                    SendingClass packetData = new();
                    packetData.Write1(0);
                    packetData.Write1(type);
                    packetData.Write1(0);
                    packetData.Write4(id);
                    _player.Client?.SendPak(packetData, 10017, _player.SessionID,true);
                }
                else
                {
                    _player.HeThongNhacNho("Có lỗi xảy ra khi xử lý mail!", 7, "TLE");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "AcceptMailCod Error " + ex.Message);
            }
        }

        /// <summary>
        /// Cập nhật tiền tệ khi thực hiện giao dịch mail
        /// </summary>
        public void MailSystem_Currency_Update(long price, CurrencyOperation operation)
        {
            if (operation == CurrencyOperation.Increase)
            {
                _player.KiemSoatGold_SoLuong(price, 1);
            }
            else
            {
                _player.KiemSoatGold_SoLuong(price, 0);
            }
            
            _player.UpdateMoneyAndWeight();
        }

        /// <summary>
        /// Từ chối mail và trả lại vật phẩm cho người gửi
        /// </summary>
        public void RejectMailCod(byte[] data)
        {
            try
            {
                int id = data[12 - offset];
                var dbTable = FindMailByID(id, (int)MailStatus.Sent);
                
                if (dbTable.Rows.Count <= 0)
                {
                    _player.HeThongNhacNho("Không tìm thấy mail này hoặc mail đã được xử lý!", 7, "TLE");
                    return;
                }
                
                var row = dbTable.Rows[0];
                
                // Cập nhật trạng thái mail
                if (UpdateMailStatusByID(id, (int)MailStatus.Rejected))
                {
                    SendingClass packetData = new();
                    packetData.Write1(0);
                    _player.Client?.SendPak(packetData,10529,_player.SessionID,true);
                    // Thông báo cho người gửi nếu đang online
                    var sender = row["SENDER"].ToString();
                    foreach (var character in World.allConnectedChars.Values)
                    {
                        if (character.CharacterName == sender)
                        {
                            character.HeThongNhacNho($"[{_player.CharacterName}] đã từ chối bưu phẩm của bạn!", 7, "TLE");
                            SendMailCodNotification(character);
                            //SendNewCodIdNotification(character, 123);
                            break;
                        }
                    }
                    
                    _player.HeThongNhacNho("Đã từ chối nhận hàng!", 7, "TLE");
                }
                else
                {
                    _player.HeThongNhacNho("Có lỗi xảy ra khi xử lý mail!", 7, "TLE");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "RejectMailCod Error " + ex.Message);
            }
        }

        /// <summary>
        /// Gửi thông báo có mail mới
        /// </summary>
        public void SendMailCodNotification(Players player)
        {
            var array = Converter.HexStringToByte("aa550000d9022f21060000000000050055aa");
            System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID),0,array,4,2);
            player.Client.Send_Map_Data(array,array.Length);
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info,"Send Notification To "+player.CharacterName);
        }

        /// <summary>
        /// Gửi thông báo có mail mới từ admin
        /// </summary>
        public void SendMailCodNotificationByAdmin(int SessionId)
        {
            SendingClass sendingClass = new();
            sendingClass.Write4(1);
            sendingClass.Write2(0);
            _player.Client?.SendPak(sendingClass, 12065, SessionId,true);
        }


        /// <summary>
        /// Gửi thông báo có mail mới với ID cụ thể
        /// </summary>
        public void SendNewCodIdNotification(Players player, int id)
        {
            var array = Converter.HexStringToByte("aa550000d902302104000000000055aa");
            System.Buffer.BlockCopy(BitConverter.GetBytes(id),0,array,14,4);
            System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID),0,array,4,2);
            player.Client?.Send_Map_Data(array,array.Length);
        }


        /// <summary>
        /// Kiểm tra xem người chơi có mail nào không
        /// </summary>
        public void CheckIfUserHaveMailCod(Players player)
        {
            var dbTable = FetchAdminMailForCharacter(player.CharacterName);
            if (dbTable.Rows.Count > 0)
            {
                SendMailCodNotificationByAdmin(player.SessionID);
            }
            
            var dbTable2 = FetchMailForCharacter(player.CharacterName);
            if (dbTable2.Rows.Count > 0)
            {
                SendMailCodNotification(player);
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Tìm vị trí vật phẩm trong túi đồ theo ID toàn cục
        /// </summary>
        private int FindItemPositionByGlobalID(long globalId)
        {
            return _player.FindItemPositionByGlobalID(globalId);
        }


        /// <summary>
        /// Kiểm tra xem người chơi có tồn tại không
        /// </summary>
        private bool CheckPlayerExists(string characterName)
        {
            var query = "SELECT TOP 1 1 FROM TBL_XWWL_Char WHERE FLD_NAME = @name";
            var sqlParameters = new[] { new SqlParameter("@name", characterName) };
            var result = DBA.GetDBToDataTable(query, sqlParameters);
            return result.Rows.Count > 0;
        }

        /// <summary>
        /// Lấy danh sách mail nhận được
        /// </summary>
        private DataTable FetchMailForCharacter(string characterName)
        {
            var query = "SELECT * FROM MailCod WHERE RECEIVER = @receiver AND STATUS = @status";
            var sqlParameters = new[] { new SqlParameter("@receiver", characterName), new SqlParameter("@status", (int)MailStatus.Sent) };
            return DBA.GetDBToDataTable(query, sqlParameters, "BBG");
        }

        /// <summary>
        /// Lấy danh sách mail từ admin
        /// </summary>
        private DataTable FetchAdminMailForCharacter(string characterName)
        {
            var query = "SELECT * FROM MailCod WHERE RECEIVER = @receiver AND STATUS = @status";
            var sqlParameters = new[] { new SqlParameter("@receiver", characterName), new SqlParameter("@status", (int)MailStatus.AdminMail) };
            return DBA.GetDBToDataTable(query, sqlParameters, "BBG");
        }

        /// <summary>
        /// Lấy danh sách mail đã gửi
        /// </summary>
        private DataTable GetMailSendByCharacter(string characterName)
        {
            var query = "SELECT * FROM MailCod WHERE SENDER = @sender";
            var sqlParameters = new[] { new SqlParameter("@sender", characterName) };
            return DBA.GetDBToDataTable(query, sqlParameters, "BBG");
        }

        /// <summary>
        /// Cập nhật trạng thái mail
        /// </summary>
        private bool UpdateMailStatusByID(int id, int status)
        {
            var query = "UPDATE MailCod SET STATUS = @status WHERE ID = @id";
            var sqlParameters = new[] { new SqlParameter("@id", id), new SqlParameter("@status", status) };
            return DBA.ExeSqlCommand(query, sqlParameters, "BBG").GetAwaiter().GetResult() > 0;
        }

        /// <summary>
        /// Cập nhật trạng thái mail và đánh dấu đã thanh toán
        /// </summary>
        private bool UpdateMailStatusByID(int id, int status, bool accept)
        {
            var query = "UPDATE MailCod SET STATUS = @status, PAID = @paid WHERE ID = @id";
            var sqlParameters = new[]
            {
                new SqlParameter("@id", id),
                new SqlParameter("@status", status),
                new SqlParameter("@paid", accept)
            };
            return DBA.ExeSqlCommand(query, sqlParameters, "BBG").GetAwaiter().GetResult() > 0;
        }

        /// <summary>
        /// Tìm mail theo ID và trạng thái
        /// </summary>
        private DataTable FindMailByID(int id, int status)
        {
            var query = "SELECT * FROM MailCod WHERE ID = @id AND STATUS = @status";// AND RECEIVER = @receiver";
            if (status == 3 || status == 6)
            {
                query += " AND RECEIVER = @receiver";
            }
            else
            {
                query += " AND SENDER = @receiver";
            }
            //LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, query +" " + status + " " + _player.CharacterName);
            var sqlParameters = new[] { new SqlParameter("@id", id), new SqlParameter("@status", status), new SqlParameter("@receiver", _player.CharacterName) };
            return DBA.GetDBToDataTable(query, sqlParameters, "BBG");
        }

        /// <summary>
        /// Tìm mail đã nhận theo ID và trạng thái
        /// </summary>
        private DataTable FindReceiveMailByID(int id, int status)
        {
            var query = "SELECT * FROM MailCod WHERE ID = @id AND STATUS = @status AND SENDER = @sender";
            var sqlParameters = new[] { new SqlParameter("@id", id), new SqlParameter("@status", status), new SqlParameter("@sender", _player.CharacterName) };
            return DBA.GetDBToDataTable(query, sqlParameters, "BBG");
        }

        /// <summary>
        /// Gửi phản hồi danh sách mail trống
        /// </summary>
        private void SendEmptyMailResponse()
        {
            var array = Converter.HexStringToByte("aa5500002b0621210b00000000001e00000000000055aa");
            System.Buffer.BlockCopy(BitConverter.GetBytes(_player.SessionID), 0, array, 4, 2);
            _player.Client?.Send_Map_Data(array, array.Length);
        }

        /// <summary>
        /// Gửi phản hồi danh sách mail
        /// </summary>
        private void SendMailListResponse(DataTable receivedMails, DataTable sentMails, DataTable adminMails)
        {
            int adminMailCount = adminMails?.Rows.Count ?? 0;
            int totalMail = receivedMails.Rows.Count + sentMails.Rows.Count + adminMailCount;

            var packetData = new SendingClass();
            packetData.Write2(sentMails.Rows.Count); // Số mail đã gửi
            packetData.Write2(receivedMails.Rows.Count); // Số mail đã nhận
            packetData.Write2(MAX_MAIL_COUNT - totalMail); // Số lượng mail có thể gửi
            packetData.Write1(0);
            packetData.Write2(totalMail); // số lượng mail sẽ hiển thị
            packetData.Write2(totalMail); // Số lượng mail sẽ hiển thị

            // Thêm mail đã nhận
            AddMailsToPacket(packetData, receivedMails);
            
            // Thêm mail đã gửi
            AddMailsToPacket(packetData, sentMails);
            
            // Thêm mail từ admin nếu có
            if (adminMails != null && adminMails.Rows.Count > 0)
            {
                AddMailsToPacket(packetData, adminMails);
            }

            _player.Client?.SendPak(packetData, 8481, _player.SessionID, true);
        }

        /// <summary>
        /// Thêm thông tin mail vào gói dữ liệu
        /// </summary>
        private void AddMailsToPacket(SendingClass packetData, DataTable mailTable)
        {
            foreach (DataRow item in mailTable.Rows)
            {
                packetData.Write4((int)item["STATUS"]);
                packetData.Write4((int)item["ID"]);
                packetData.WriteString(item["SENDER"].ToString(), 14);
                packetData.Write1(0);
                packetData.WriteString(item["RECEIVER"].ToString(), 14);
                packetData.Write1(0);
                packetData.Write2(DESCRIPTION_LENGTH);
                packetData.Write(EnsureExactByteLength((byte[])item["DESCRIPTION"], DESCRIPTION_LENGTH));
                packetData.Write(new byte[12]);
                var mailCod = (byte[])item["ITEMBYTE"];
                var marketItem = new X_Vat_Pham_Loai();
                marketItem.VatPham_byte = mailCod;
                var byteArray = marketItem.GetByte();
                packetData.Write(byteArray);
                packetData.Write8((long)item["PRICE"]);
                var dateTime = (DateTime)item["CREATED_AT"];
                packetData.Write1(dateTime.Day);
                packetData.Write1(dateTime.Month);
                packetData.Write1(dateTime.Month);
                packetData.Write1(dateTime.Hour);
                packetData.Write1(dateTime.Minute);
                packetData.Write(new byte[3]);
                packetData.Write(new byte[28]);
            }
        }

        /// <summary>
        /// Lưu mail vào cơ sở dữ liệu
        /// </summary>
        private bool SaveMailToDatabase(string sender, string receiver, X_Vat_Pham_Loai item, double price, byte[] descriptionByte)
        {
            var query = """
                        INSERT INTO MailCod (SENDER, RECEIVER, ITEM_NAME, ITEMBYTE, PRICE, STATUS, PAID, DESCRIPTION, CREATED_AT, EXPIRED_AT) 
                        VALUES (@sender, @receiver, @itemName, @itemByte, @price, @status, @paid, @description, @createdAt, @expiredAt)
                        """;
            
            World.ItemList.TryGetValue((int)item.GetVatPham_ID, out var itemInfo);
            var sqlParameters = new List<SqlParameter>
            {
                new("@sender", sender),
                new("@description", descriptionByte),
                new("@itemName", itemInfo != null ? itemInfo.ItmeNAME : ""),
                new("@receiver", receiver),
                new("@itemByte", item.VatPham_byte),
                new("@price", price),
                new("@paid", false),
                new("@status", (int)MailStatus.Sent),
                new("@createdAt", DateTime.Now),
                new("@expiredAt", DateTime.Now.AddDays(1)),
            };
            
            return DBA.ExeSqlCommand(query, sqlParameters.ToArray(), "BBG").GetAwaiter().GetResult() > 0;
        }

        /// <summary>
        /// Cập nhật túi đồ sau khi gửi vật phẩm
        /// </summary>
        private void UpdateInventoryAfterSendingItem(int itemPos, int amountLeft)
        {
            if (amountLeft <= 0)
                _player.Item_In_Bag[itemPos].VatPham_byte = new byte[World.Item_Db_Byte_Length];
            else
                _player.Item_In_Bag[itemPos].VatPhamSoLuong = BitConverter.GetBytes(amountLeft);
            
            _player.Init_Item_In_Bag();
        }

        /// <summary>
        /// Thông báo cho người nhận nếu đang online
        /// </summary>
        private void NotifyReceiverIfOnline(string receiver, string sender)
        {
            foreach (var character in World.allConnectedChars.Values)
            {
                if (character.CharacterName == receiver)
                {
                    //LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Found Character " + receiver);
                    character.HeThongNhacNho($"Bạn có một bưu phẩm Từ [{sender}! Vui lòng tới [Bát Quái Lão Nhân] ở [Huyền Bột phái] để nhận!!!]", 7, "TLE");
                    SendMailCodNotification(character);
                    //SendNewCodIdNotification(character, 123);
                    break;
                }
            }
        }

        /// <summary>
        /// Đảm bảo mảng byte có độ dài chính xác
        /// </summary>
        private byte[] EnsureExactByteLength(byte[] source, int exactLength)
        {
            if (source.Length == exactLength)
                return source;

            var result = new byte[exactLength];
            System.Buffer.BlockCopy(source, 0, result, 0, Math.Min(source.Length, exactLength));
            return result;
        }

        #endregion
    }

    // /// <summary>
    // /// Loại thao tác tiền tệ
    // /// </summary>
    // public enum CurrencyOperation
    // {
    //     Add,
    //     Subtract
    // }
}