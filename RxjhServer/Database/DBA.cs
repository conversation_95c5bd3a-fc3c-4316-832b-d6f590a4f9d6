using Microsoft.EntityFrameworkCore;
using System;
using System.Data;
using System.Data.Common;
using System.Threading.Tasks;
using Microsoft.Data.SqlClient;
using HeroYulgang.Core;

namespace RxjhServer.Database
{
    /// <summary>
    /// Lớp tương thích với DBA cũ, sử dụng Entity Framework Core với DatabaseManager singleton
    /// </summary>
    public static class DBA
    {
        private static readonly object _lock = new object();

        /// <summary>
        /// Lấy DbContext dựa trên tên database từ DatabaseManager singleton
        /// </summary>
        private static DbContext GetDbContext(string databaseName)
        {
            return databaseName.ToLower() switch
            {
                "accountdb" or "account" or "rxjhaccount" => HeroYulgang.Core.DatabaseManager.Instance.AccountDb,
                "gamedb" or "game" or "gameserver" => HeroYulgang.Core.DatabaseManager.Instance.GameDb,
                "publicdb" or "public" => HeroYulgang.Core.DatabaseManager.Instance.PublicDb,
                "webdb" or "web" or "bbg" => HeroYulgang.Core.DatabaseManager.Instance.BbgDbContext,
                _ => throw new ArgumentException($"Không hỗ trợ cơ sở dữ liệu: {databaseName}"),
            };
        }

        /// <summary>
        /// Thực thi truy vấn SQL và xử lý kết quả bằng hàm xử lý được cung cấp (Thread-safe)
        /// </summary>
        private static T ExecuteDbCommand<T>(string sql, SqlParameter[] parameters, string databaseName, Func<DbCommand, T> handler, bool isStoredProcedure = false)
        {
            lock (_lock)
            {
                try
                {
                    var context = GetDbContext(databaseName);
                    var connection = context.Database.GetDbConnection();

                    using var command = connection.CreateCommand();
                    command.CommandText = sql;

                    // Thiết lập CommandType dựa trên loại command
                    command.CommandType = isStoredProcedure ? CommandType.StoredProcedure : CommandType.Text;

                    if (parameters != null)
                    {
                        foreach (var parameter in parameters)
                        {
                            command.Parameters.Add(parameter);
                        }
                    }

                    // Đảm bảo connection được mở đúng cách và không bị conflict
                    bool wasOpen = connection.State == ConnectionState.Open;
                    if (!wasOpen)
                    {
                        connection.Open();
                    }

                    try
                    {
                        var result = handler(command);
                        return result;
                    }
                    finally
                    {
                        // Chỉ đóng connection nếu chúng ta đã mở nó
                        if (!wasOpen && connection.State == ConnectionState.Open)
                        {
                            connection.Close();
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Lỗi khi thực hiện truy vấn ExecuteDbCommand: {ex.Message} {sql}");
                    return default;
                }
            }
        }

        /// <summary>
        /// Lấy dữ liệu từ cơ sở dữ liệu và trả về DataTable
        /// </summary>
        public static DataTable GetDBToDataTable(string sql, string databaseName = "GameDb")
        {
            var dataTable = new DataTable();

            return ExecuteDbCommand(sql, null, databaseName, command =>
            {
                using var reader = command.ExecuteReader();
                dataTable.Load(reader);
                return dataTable;
            }) ?? new DataTable();
        }

        /// <summary>
        /// Lấy dữ liệu từ cơ sở dữ liệu và trả về DataTable với tham số
        /// </summary>
        public static DataTable GetDBToDataTable(string sql, SqlParameter[] parameters, string databaseName = "GameDb")
        {
            var dataTable = new DataTable();

            return ExecuteDbCommand(sql, parameters, databaseName, command =>
            {
                using var reader = command.ExecuteReader();
                dataTable.Load(reader);
                return dataTable;
            }) ?? new DataTable();
        }

        /// <summary>
        /// Lấy giá trị đơn từ cơ sở dữ liệu
        /// </summary>
        public static object GetDBValue_3(string sql, string databaseName = "GameDb")
        {
            return ExecuteDbCommand(sql, null, databaseName, command => command.ExecuteScalar());
        }

        public static object GetDBValue_3(string sql, SqlParameter[] parameters, string databaseName = "GameDb")
        {
            return ExecuteDbCommand(sql, parameters, databaseName, command => command.ExecuteScalar());
        }

        /// <summary>
        /// Thực thi lệnh SQL không trả về dữ liệu với tham số (Sync version)
        /// </summary>
        public static int ExeSqlCommandSync(string sql, SqlParameter[] parameters, string databaseName = "GameDb")
        {
            return ExecuteDbCommand(sql, parameters, databaseName, command => command.ExecuteNonQuery());
        }

        /// <summary>
        /// Thực thi lệnh SQL không trả về dữ liệu (Sync version)
        /// </summary>
        public static int ExeSqlCommandSync(string sql, string databaseName = "GameDb")
        {
            return ExecuteDbCommand(sql, null, databaseName, command => command.ExecuteNonQuery());
        }

        /// <summary>
        /// Thực thi stored procedure với tham số
        /// </summary>
        public static int ExeStoredProcedure(string procedureName, SqlParameter[] parameters, string databaseName = "GameDb")
        {
            return ExecuteDbCommand(procedureName, parameters, databaseName, command => command.ExecuteNonQuery(), true);
        }

        /// <summary>
        /// Thực thi stored procedure không có tham số
        /// </summary>
        public static int ExeStoredProcedure(string procedureName, string databaseName = "GameDb")
        {
            return ExecuteDbCommand(procedureName, null, databaseName, command => command.ExecuteNonQuery(), true);
        }

        /// <summary>
        /// Thực thi stored procedure và trả về DataTable
        /// </summary>
        public static DataTable GetStoredProcedureToDataTable(string procedureName, SqlParameter[] parameters, string databaseName = "GameDb")
        {
            var dataTable = new DataTable();
            return ExecuteDbCommand(procedureName, parameters, databaseName, command =>
            {
                using var reader = command.ExecuteReader();
                dataTable.Load(reader);
                return dataTable;
            }, true) ?? new DataTable();
        }

        /// <summary>
        /// Thực thi stored procedure và trả về giá trị đơn
        /// </summary>
        public static object GetStoredProcedureValue(string procedureName, SqlParameter[] parameters, string databaseName = "GameDb")
        {
            return ExecuteDbCommand(procedureName, parameters, databaseName, command => command.ExecuteScalar(), true);
        }

        /// <summary>
        /// Thực thi stored procedure async với tham số
        /// </summary>
        public static async Task<int> ExeStoredProcedureAsync(string procedureName, SqlParameter[] parameters, string databaseName = "GameDb")
        {
            return await Task.Run(() => ExeStoredProcedure(procedureName, parameters, databaseName));
        }

        /// <summary>
        /// Thực thi stored procedure async không có tham số
        /// </summary>
        public static async Task<int> ExeStoredProcedureAsync(string procedureName, string databaseName = "GameDb")
        {
            return await Task.Run(() => ExeStoredProcedure(procedureName, databaseName));
        }

        /// <summary>
        /// Thực thi stored procedure async và trả về DataTable
        /// </summary>
        public static async Task<DataTable> GetStoredProcedureToDataTableAsync(string procedureName, SqlParameter[] parameters, string databaseName = "GameDb")
        {
            return await Task.Run(() => GetStoredProcedureToDataTable(procedureName, parameters, databaseName));
        }

        /// <summary>
        /// Thực thi stored procedure async và trả về giá trị đơn
        /// </summary>
        public static async Task<object> GetStoredProcedureValueAsync(string procedureName, SqlParameter[] parameters, string databaseName = "GameDb")
        {
            return await Task.Run(() => GetStoredProcedureValue(procedureName, parameters, databaseName));
        }

        /// <summary>
        /// Thực thi truy vấn SQL async và trả về DataTable
        /// </summary>
        public static async Task<DataTable> GetDBToDataTableAsync(string sql, string databaseName = "GameDb")
        {
            return await Task.Run(() => GetDBToDataTable(sql, databaseName));
        }

        /// <summary>
        /// Thực thi truy vấn SQL async với tham số và trả về DataTable
        /// </summary>
        public static async Task<DataTable> GetDBToDataTableAsync(string sql, SqlParameter[] parameters, string databaseName = "GameDb")
        {
            return await Task.Run(() => GetDBToDataTable(sql, parameters, databaseName));
        }

        /// <summary>
        /// Thực thi lệnh SQL async không trả về dữ liệu với tham số
        /// </summary>
        public static async Task<int> ExeSqlCommandAsync(string sql, SqlParameter[] parameters, string databaseName = "GameDb")
        {
            return await Task.Run(() => ExeSqlCommandSync(sql, parameters, databaseName));
        }

        /// <summary>
        /// Thực thi lệnh SQL async không trả về dữ liệu
        /// </summary>
        public static async Task<int> ExeSqlCommandAsync(string sql, string databaseName = "GameDb")
        {
            return await Task.Run(() => ExeSqlCommandSync(sql, databaseName));
        }

        /// <summary>
        /// Backward compatibility: ExeSqlCommand với GetAwaiter().GetResult() pattern
        /// Trả về Task để tương thích với code cũ sử dụng .GetAwaiter().GetResult()
        /// </summary>
        public static Task<int> ExeSqlCommand(string sql, string databaseName = "GameDb")
        {
            return Task.FromResult(ExeSqlCommandSync(sql, databaseName));
        }

        /// <summary>
        /// Backward compatibility: ExeSqlCommand với GetAwaiter().GetResult() pattern
        /// Trả về Task để tương thích với code cũ sử dụng .GetAwaiter().GetResult()
        /// </summary>
        public static Task<int> ExeSqlCommand(string sql, SqlParameter[] parameters, string databaseName = "GameDb")
        {
            return Task.FromResult(ExeSqlCommandSync(sql, parameters, databaseName));
        }

        /// <summary>
        /// Lấy giá trị đơn async từ cơ sở dữ liệu
        /// </summary>
        public static async Task<object> GetDBValue_3Async(string sql, string databaseName = "GameDb")
        {
            return await Task.Run(() => GetDBValue_3(sql, databaseName));
        }

        /// <summary>
        /// Lấy giá trị đơn async từ cơ sở dữ liệu với tham số
        /// </summary>
        public static async Task<object> GetDBValue_3Async(string sql, SqlParameter[] parameters, string databaseName = "GameDb")
        {
            return await Task.Run(() => GetDBValue_3(sql, parameters, databaseName));
        }
    }
}
