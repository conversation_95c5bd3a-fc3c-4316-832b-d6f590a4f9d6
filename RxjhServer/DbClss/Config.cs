using System;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.Json;
using System.Collections.Generic;
using HeroYulgang.Services;
using HeroYulgang.Helpers;

namespace RxjhServer.DbClss;

public class Config
{
    private static string string_0 = Path.Combine(Directory.GetCurrentDirectory(), "config.json");
    private static Dictionary<string, Dictionary<string, string>> jsonConfig;
    private static bool isJsonConfigLoaded = false;
    private static readonly object configLock = new object();

    static Config()
    {
        try
        {
            // Kiểm tra xem file config.json có tồn tại không
            if (!File.Exists(string_0))
            {
                // Thử tìm trong thư mục gốc của ứng dụng
                string altPath = Path.Combine(AppContext.BaseDirectory, "config.json");
                if (File.Exists(altPath))
                {
                    string_0 = altPath;
                    LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Sử dụng file cấu hình từ thư mục ứng dụng: {string_0}");
                }
                else
                {
                    LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Không tìm thấy file cấu hình tại: {string_0} hoặc {altPath}");
                }
            }
            else
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Sử dụng file cấu hình: {string_0}");
            }
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi khởi tạo Config: {ex.Message}");
        }
    }

    public static string ConfigPath
    {
        get { return string_0; }
        set
        {
            if (string.IsNullOrEmpty(value))
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Đường dẫn file cấu hình không được để trống");
                return;
            }

            string_0 = value;
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã cập nhật đường dẫn file cấu hình: {string_0}");

            // Reset cấu hình khi đường dẫn thay đổi
            isJsonConfigLoaded = false;
            jsonConfig = null;
        }
    }

    // Phương thức để đọc và phân tích file JSON
    private static void LoadJsonConfig()
    {
        lock (configLock)
        {
            try
            {
                if (!File.Exists(string_0))
                {
                    Console.WriteLine($"[Lỗi] Không tìm thấy file cấu hình: {string_0}");
                    Console.WriteLine($"[Lỗi] Đường dẫn đầy đủ: {Path.GetFullPath(string_0)}");
                    Console.WriteLine($"[Lỗi] Thư mục hiện tại: {Directory.GetCurrentDirectory()}");
                    jsonConfig = new Dictionary<string, Dictionary<string, string>>();
                    return;
                }

                string jsonContent = File.ReadAllText(string_0);

                if (string.IsNullOrWhiteSpace(jsonContent))
                {
                    Console.WriteLine($"[Lỗi] File cấu hình {string_0} rỗng hoặc chỉ chứa khoảng trắng");
                    jsonConfig = new Dictionary<string, Dictionary<string, string>>();
                    return;
                }

                try
                {
                    using JsonDocument document = JsonDocument.Parse(jsonContent);
                    JsonElement root = document.RootElement;

                    var newConfig = new Dictionary<string, Dictionary<string, string>>(StringComparer.OrdinalIgnoreCase);

                    foreach (JsonProperty section in root.EnumerateObject())
                    {
                        var sectionDict = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);

                        if (section.Value.ValueKind == JsonValueKind.Object)
                        {
                            foreach (JsonProperty property in section.Value.EnumerateObject())
                            {
                                sectionDict[property.Name] = property.Value.ToString();
                            }
                        }

                        newConfig[section.Name] = sectionDict;
                    }

                    jsonConfig = newConfig;
                    Console.WriteLine($"[Thông báo] Đã tải thành công file cấu hình: {string_0}");
                    Console.WriteLine($"[Thông báo] Số lượng section: {jsonConfig.Count}");
                    isJsonConfigLoaded = true;
                }
                catch (JsonException jsonEx)
                {
                    Console.WriteLine($"[Lỗi] File cấu hình {string_0} không đúng định dạng JSON: {jsonEx.Message}");
                    jsonConfig = new Dictionary<string, Dictionary<string, string>>();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[Lỗi] Khi đọc file cấu hình JSON: {ex.Message}");
                Console.WriteLine($"[Lỗi] Stack trace: {ex.StackTrace}");
                jsonConfig = new Dictionary<string, Dictionary<string, string>>();
            }
        }
    }

    // Phương thức đọc giá trị từ cấu hình JSON
    public static string IniReadValue(string section, string key)
    {
        var text = section + " " + key;
        try
        {
            // Đảm bảo cấu hình JSON đã được nạp
            if (!isJsonConfigLoaded)
            {
                LoadJsonConfig();
            }

            // Kiểm tra section có tồn tại không
            if (jsonConfig.TryGetValue(section.ToLower(), out var sectionDict))
            {
                // Kiểm tra key có tồn tại không
                if (sectionDict.TryGetValue(key.ToLower(), out var value))
                {
                    return value ?? "";
                }
            }

            // Log thông báo khi không tìm thấy section hoặc key
            Console.WriteLine($"Không tìm thấy cấu hình cho section '{section}', key '{key}'. Trả về giá trị mặc định");

            // Trả về "0" thay vì chuỗi rỗng để tránh lỗi khi parse
            return "";
        }
        catch (Exception ex)
        {
            Console.WriteLine($"{text} cấu hình JSON: {ex.Message}");
            return ""; // Trả về "0" thay vì chuỗi rỗng
        }
    }
}
