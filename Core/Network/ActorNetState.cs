using System;
using System.Net;
using Akka.Actor;
using Akka.IO;
using HeroYulgang.Core.Actors;
using HeroYulgang.Helpers;
using HeroYulgang.Services;
using HeroYulgang.Utils;
using RxjhServer;
using RxjhServer.HelperTools;
using RxjhServer.Network;

namespace HeroYulgang.Core.Network
{
    /// <summary>
    /// Lớp ActorNetState thay thế cho NetState, sử dụng Akka.NET để gửi packet
    /// </summary>
    public class ActorNetState
    {
        private readonly IActorRef _connection;
        private readonly int _sessionId;
        private readonly IPEndPoint _remoteEndPoint;
        private bool _running = true;
        private bool _treoMay = false;
        private bool _online = false;

        private bool _login = false;
        public bool Login
        {
            get => _login;
            set => _login = value;
        }
        public bool Online
        {
            get => _online;
            set => _online = value;
        }
        public byte[] g_cur_key { get; set; }
        private Players? _player;

        public bool VersionVerification { get; set; } = false;

        /// <summary>
        /// Khởi tạo một ActorNetState mới
        /// </summary>
        /// <param name="connection"><PERSON><PERSON> chi<PERSON>u đến actor kết nối</param>
        /// <param name="sessionId">ID phiên kết nối</param>
        /// <param name="remoteEndPoint">Địa chỉ IP và port của client</param>
        public ActorNetState(IActorRef connection, int sessionId, IPEndPoint remoteEndPoint)
        {
            _connection = connection;
            _sessionId = sessionId;
            _remoteEndPoint = remoteEndPoint;
            World.list[sessionId] = this;
        }

        public bool ThoatGame { get; private set; }

        /// <summary>
        /// Lấy hoặc đặt trạng thái treo máy
        /// </summary>
        public bool TreoMay
        {
            get => _treoMay;
            set => _treoMay = value;
        }

        /// <summary>
        /// Lấy hoặc đặt ID thế giới
        /// </summary>
        public int SessionID
        {
            get => _sessionId;
        }

        /// <summary>
        /// Lấy hoặc đặt tham chiếu đến đối tượng Player
        /// </summary>
        public Players? Player
        {
            get => _player;
            set => _player = value;
        }

        /// <summary>
        /// Kiểm tra xem kết nối có đang chạy không
        /// </summary>
        public bool Running => _running;

        public bool BindAccount { get; internal set; }

        /// <summary>
        /// Gửi dữ liệu đến client
        /// </summary>
        /// <param name="data">Dữ liệu cần gửi</param>
        /// <param name="length">Độ dài dữ liệu</param>
        public void Send(byte[] data, int length)
        {
            try
            {
                if (!_running) return;

                // Mã hóa dữ liệu trước khi gửi
                var encryptedData = Crypto.EncryptPacket(data);

                // Gửi dữ liệu thông qua actor
                var tcpManager = ActorSystemManager.Instance.ActorSystem.ActorSelection("/user/tcpManager");
                tcpManager.Tell(new SendPacket(_connection, encryptedData));

                // Ghi log nếu cần
                if (RxjhServer.World.Debug > 0)
                {
                    LogHelper.WriteLine(LogLevel.Debug, $"Gửi dữ liệu đến client {_sessionId}: {RxjhServer.HelperTools.Converter.ToString(data)}");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi gửi dữ liệu đến client {_sessionId}: {ex.Message}");
            }
        }

        public void SendSinglePackage(byte[] toSendBuff, int len)
        {
            var array = new byte[BitConverter.ToInt16(toSendBuff, 9) + 7];
            System.Buffer.BlockCopy(toSendBuff, 5, array, 0, array.Length);
            SendSinglePackage_PackageTransmit(array, array.Length);
        }

        private void SendSinglePackage_PackageTransmit(byte[] toSendBuff, int length)
        {
            var array = new byte[length + 15];
            array[0] = 170;
            array[1] = 85;
            System.Buffer.BlockCopy(BitConverter.GetBytes(length + 9), 0, array, 2, 2);
            System.Buffer.BlockCopy(toSendBuff, 0, array, 5, length);
            array[array.Length - 2] = 85;
            array[array.Length - 1] = 170;
            Send(array, array.Length);
        }

        public void Send_Map_Data(byte[] byte_0, int int_1)
        {
            var array = new byte[int_1 + 2];
            array[0] = 170;
            array[1] = 85;
            System.Buffer.BlockCopy(BitConverter.GetBytes(int_1 - 4), 0, array, 2, 2);
            System.Buffer.BlockCopy(byte_0, 4, array, 4, 2);
            System.Buffer.BlockCopy(byte_0, 6, array, 8, int_1 - 8);
            array[array.Length - 2] = 85;
            array[array.Length - 1] = 170;
            Send(array, array.Length);
        }

        public void SendPak(SendingClass pak, int id, int wordid, bool bypass = false)
        {
            try
            {
                var array = pak.ToArray2(id, wordid);
                SendMultiplePackageEncryptionByPass(array, array.Length, 1);
                return;
            }
            catch (Exception)
            {
            }
        }

        private void SendMultiplePackageEncryptionByPass(byte[] toSendBuff, int length, int xl)
            {
            try
            {
                var array = new byte[toSendBuff.Length + 6];
                array[0] = 170;
                array[1] = 85;
                System.Buffer.BlockCopy(BitConverter.GetBytes(toSendBuff.Length), 0, array, 2, 2);
                System.Buffer.BlockCopy(toSendBuff, 0, array, 4, toSendBuff.Length);
                array[^2] = 85;
                array[^1] = 170;
                Send(array, array.Length);
            }
            catch (Exception ex)
            {
                    LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi gửi dữ liệu đến client {_sessionId}: {ex.Message}");
                    ///Form1.WriteLine(1, "Send()_SendMultiplePackageEncryption" + WorldId + "|" + ex.Message);
            }
            }    

        public void SendMultiplePackage(byte[] toSendBuff, int len)
        {
            Send_Map_Data(toSendBuff, len); 
        }
        private void SendMultiplePackage_PackageTransmit(byte[] toSendBuff, int length, int xl)
        {
            var array = new byte[length + 20];
            array[0] = 170;
            array[1] = 85;
            System.Buffer.BlockCopy(BitConverter.GetBytes(length + 14), 0, array, 2, 2);
            System.Buffer.BlockCopy(BitConverter.GetBytes(xl), 0, array, 4, 4);
            System.Buffer.BlockCopy(toSendBuff, 0, array, 8, length);
            array[^2] = 85;
            array[^1] = 170;
            Send(array, array.Length);
        }
        /// <summary>
        /// Đóng kết nối và giải phóng tài nguyên
        /// </summary>
        public void Dispose()
        {
            _running = false;

            // Thông báo cho TcpManagerActor để đóng kết nối
            try
            {
                if (World.list.TryGetValue(_sessionId, out var value))
                {
                    World.list.Remove(_sessionId);
                }
                var tcpManager = ActorSystemManager.Instance.ActorSystem.ActorSelection("/user/tcpManager");
                tcpManager.Tell(new CloseConnection(_connection));
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi đóng kết nối client {_sessionId}: {ex.Message}");
            }
        }

        /// <summary>
        /// Chuyển đổi thành chuỗi
        /// </summary>
        public override string ToString()
        {
            return _remoteEndPoint.Address.ToString();
        }
        public  int GetPort()
        {
            return _remoteEndPoint.Port;
        }

        internal void DisposedOffline()
        {
            Dispose();
        }

        internal void Offline()
        {
            var players = World.FindPlayerBySession(SessionID);
            ThoatGame = true;
            TreoMay = true;
            if (players != null)
            {
                if (TreoMay && players.Offline_TreoMay_Mode_ON_OFF == 1)
                {
                    World.TreoMay_Offline++;
                }
                else if (TreoMay && players.Offline_TreoMay_Mode_ON_OFF == 0)
                {
                    World.OffLine_SoLuong++;
                }
            }
        }
    }
}
